<template>
  <div class="health-plan-panel">
    <div class="panel-header">
      <h3>个性化调养方案</h3>
      <p class="panel-desc">根据您的健康状况制定专属调养计划</p>
    </div>

    <!-- 方案类型选择 -->
    <div class="plan-categories">
      <div 
        v-for="(category, index) in planCategories" 
        :key="index"
        class="category-tab"
        :class="{ active: activeCategory === index }"
        @click="selectCategory(index)"
      >
        <span class="category-icon">{{ category.icon }}</span>
        <span class="category-name">{{ category.name }}</span>
      </div>
    </div>

    <!-- 当前方案内容 -->
    <div class="plan-content" v-if="activeCategory !== -1">
      <div class="plan-overview">
        <h4>{{ currentPlan.title }}</h4>
        <p class="plan-description">{{ currentPlan.description }}</p>
        
        <div class="plan-stats">
          <div class="stat-item">
            <span class="stat-label">周期</span>
            <span class="stat-value">{{ currentPlan.duration }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">难度</span>
            <span class="stat-value">{{ currentPlan.difficulty }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">目标</span>
            <span class="stat-value">{{ currentPlan.target }}</span>
          </div>
        </div>
      </div>

      <!-- 详细计划 -->
      <div class="plan-details">
        <div class="detail-section" v-for="(section, sIndex) in currentPlan.sections" :key="sIndex">
          <h5 class="section-title">
            <span class="section-icon">{{ section.icon }}</span>
            {{ section.title }}
          </h5>
          
          <div class="section-content">
            <div class="plan-item" v-for="(item, iIndex) in section.items" :key="iIndex">
              <div class="item-header">
                <span class="item-title">{{ item.title }}</span>
                <span class="item-time">{{ item.time }}</span>
              </div>
              <p class="item-description">{{ item.description }}</p>
              <div class="item-tags">
                <span 
                  v-for="(tag, tIndex) in item.tags" 
                  :key="tIndex"
                  class="item-tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 进度跟踪 -->
      <div class="progress-tracking">
        <h5>本周完成情况</h5>
        <div class="progress-grid">
          <div 
            v-for="(day, dIndex) in weekProgress" 
            :key="dIndex"
            class="day-progress"
            :class="{ completed: day.completed, today: day.isToday }"
            @click="toggleDayCompletion(dIndex)"
          >
            <div class="day-name">{{ day.name }}</div>
            <div class="day-status">
              <span v-if="day.completed">✓</span>
              <span v-else>○</span>
            </div>
          </div>
        </div>
        
        <div class="progress-summary">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: weekCompletionRate + '%' }"></div>
          </div>
          <p class="progress-text">本周完成率: {{ weekCompletionRate }}%</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="plan-actions">
        <button class="btn-primary" @click="startPlan">开始执行</button>
        <button class="btn-secondary" @click="customizePlan">个性化定制</button>
        <button class="btn-outline" @click="sharePlan">分享方案</button>
      </div>
    </div>

    <!-- 自定义方案弹窗 -->
    <div class="modal-overlay" v-if="showCustomModal" @click="closeCustomModal">
      <div class="custom-modal" @click.stop>
        <h4>个性化定制</h4>
        <div class="custom-form">
          <div class="form-group">
            <label>目标设定</label>
            <select v-model="customSettings.goal">
              <option value="weight_loss">减重塑形</option>
              <option value="muscle_gain">增肌强体</option>
              <option value="health_maintain">健康维护</option>
              <option value="recovery">康复调理</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>时间安排</label>
            <select v-model="customSettings.timeSlot">
              <option value="morning">早晨 (6:00-9:00)</option>
              <option value="afternoon">下午 (14:00-17:00)</option>
              <option value="evening">晚上 (18:00-21:00)</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>强度等级</label>
            <div class="intensity-slider">
              <input 
                type="range" 
                min="1" 
                max="5" 
                v-model="customSettings.intensity"
                class="slider"
              />
              <span class="intensity-label">{{ getIntensityLabel() }}</span>
            </div>
          </div>
        </div>
        
        <div class="modal-actions">
          <button class="btn-primary" @click="saveCustomSettings">保存设置</button>
          <button class="btn-secondary" @click="closeCustomModal">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HealthPlan',
  data() {
    return {
      activeCategory: 0,
      showCustomModal: false,
      planCategories: [
        { name: '运动健身', icon: '🏃' },
        { name: '饮食调理', icon: '🥗' },
        { name: '作息调节', icon: '😴' },
        { name: '心理调适', icon: '🧘' }
      ],
      plans: {
        0: { // 运动健身
          title: '全身塑形运动计划',
          description: '结合有氧运动和力量训练，全面提升身体素质',
          duration: '8周',
          difficulty: '中等',
          target: '减脂塑形',
          sections: [
            {
              title: '热身准备',
              icon: '🔥',
              items: [
                {
                  title: '动态拉伸',
                  time: '5-10分钟',
                  description: '全身关节活动，提高身体温度',
                  tags: ['必做', '基础']
                }
              ]
            },
            {
              title: '主要训练',
              icon: '💪',
              items: [
                {
                  title: '有氧运动',
                  time: '20-30分钟',
                  description: '跑步、游泳或骑行，提高心肺功能',
                  tags: ['核心', '燃脂']
                },
                {
                  title: '力量训练',
                  time: '15-20分钟',
                  description: '针对主要肌群的抗阻训练',
                  tags: ['塑形', '增肌']
                }
              ]
            }
          ]
        },
        1: { // 饮食调理
          title: '营养均衡饮食方案',
          description: '科学搭配营养素，促进身体健康',
          duration: '持续执行',
          difficulty: '简单',
          target: '营养均衡',
          sections: [
            {
              title: '早餐建议',
              icon: '🌅',
              items: [
                {
                  title: '优质蛋白+复合碳水',
                  time: '7:00-8:00',
                  description: '鸡蛋、燕麦、牛奶、水果的组合',
                  tags: ['营养', '饱腹']
                }
              ]
            }
          ]
        },
        2: { // 作息调节
          title: '健康作息调整计划',
          description: '建立规律作息，提高睡眠质量',
          duration: '4周',
          difficulty: '简单',
          target: '改善睡眠',
          sections: [
            {
              title: '睡前准备',
              icon: '🌙',
              items: [
                {
                  title: '放松冥想',
                  time: '21:30-22:00',
                  description: '深呼吸和冥想练习，放松身心',
                  tags: ['放松', '助眠']
                }
              ]
            }
          ]
        },
        3: { // 心理调适
          title: '心理健康调适方案',
          description: '缓解压力，保持心理健康',
          duration: '长期坚持',
          difficulty: '简单',
          target: '心理健康',
          sections: [
            {
              title: '情绪管理',
              icon: '😊',
              items: [
                {
                  title: '正念练习',
                  time: '每日10分钟',
                  description: '专注当下，观察内心感受',
                  tags: ['正念', '减压']
                }
              ]
            }
          ]
        }
      },
      weekProgress: [
        { name: '周一', completed: true, isToday: false },
        { name: '周二', completed: true, isToday: false },
        { name: '周三', completed: false, isToday: true },
        { name: '周四', completed: false, isToday: false },
        { name: '周五', completed: false, isToday: false },
        { name: '周六', completed: false, isToday: false },
        { name: '周日', completed: false, isToday: false }
      ],
      customSettings: {
        goal: 'health_maintain',
        timeSlot: 'morning',
        intensity: 3
      }
    }
  },
  computed: {
    currentPlan() {
      return this.plans[this.activeCategory] || {}
    },
    weekCompletionRate() {
      const completed = this.weekProgress.filter(day => day.completed).length
      return Math.round((completed / this.weekProgress.length) * 100)
    }
  },
  methods: {
    selectCategory(index) {
      this.activeCategory = index
    },
    toggleDayCompletion(dayIndex) {
      this.weekProgress[dayIndex].completed = !this.weekProgress[dayIndex].completed
    },
    startPlan() {
      uni.showToast({
        title: '开始执行计划！',
        icon: 'success'
      })
    },
    customizePlan() {
      this.showCustomModal = true
    },
    sharePlan() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      })
    },
    closeCustomModal() {
      this.showCustomModal = false
    },
    saveCustomSettings() {
      uni.showToast({
        title: '设置已保存',
        icon: 'success'
      })
      this.closeCustomModal()
    },
    getIntensityLabel() {
      const labels = ['很轻松', '轻松', '适中', '有挑战', '高强度']
      return labels[this.customSettings.intensity - 1]
    }
  }
}
</script>

<style scoped>
.health-plan-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(33,150,243,0.08);
}

.panel-header {
  margin-bottom: 24px;
  text-align: center;
}

.panel-header h3 {
  color: #1976d2;
  margin-bottom: 8px;
}

.panel-desc {
  color: #666;
  font-size: 14px;
}

.plan-categories {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.category-tab {
  padding: 12px 20px;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-tab:hover {
  background: #f5f5f5;
}

.category-tab.active {
  background: #e3f2fd;
  border-bottom: 2px solid #2196f3;
}

.plan-overview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.plan-overview h4 {
  color: #1976d2;
  margin-bottom: 8px;
}

.plan-description {
  color: #666;
  margin-bottom: 16px;
}

.plan-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-weight: bold;
  color: #2196f3;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1976d2;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.plan-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-title {
  font-weight: bold;
  color: #333;
}

.item-time {
  font-size: 12px;
  color: #666;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
}

.item-description {
  color: #666;
  margin-bottom: 8px;
  font-size: 14px;
}

.item-tags {
  display: flex;
  gap: 6px;
}

.item-tag {
  font-size: 10px;
  background: #4caf50;
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
}

.progress-tracking {
  background: #e8f5e8;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.progress-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.day-progress {
  text-align: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.day-progress:hover {
  background: #f0f0f0;
}

.day-progress.completed {
  background: #4caf50;
  color: white;
}

.day-progress.today {
  border: 2px solid #2196f3;
}

.day-name {
  font-size: 12px;
  margin-bottom: 4px;
}

.day-status {
  font-size: 18px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: #4caf50;
  transition: width 0.3s;
}

.progress-text {
  text-align: center;
  color: #2e7d32;
  font-weight: bold;
}

.plan-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn-primary, .btn-secondary, .btn-outline {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
}

.btn-primary {
  background: #2196f3;
  color: white;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
}

.btn-outline {
  background: transparent;
  color: #2196f3;
  border: 1px solid #2196f3;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.custom-modal {
  background: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 400px;
}

.custom-form {
  margin: 20px 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.intensity-slider {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider {
  flex: 1;
}

.intensity-label {
  font-size: 12px;
  color: #666;
  min-width: 60px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>
