/**
 * 路由管理工具
 * 统一管理页面跳转逻辑
 */

// 路由路径定义
export const ROUTES = {
  LOGIN: '/pages/login/login',
  INDEX: '/pages/index/index',
  // 可以添加更多路由
}

/**
 * 页面跳转 - 保留当前页面，跳转到应用内的某个页面
 * @param {string} url 页面路径
 * @param {Object} params 页面参数
 */
export function navigateTo(url, params = {}) {
  const queryString = formatParams(params)
  uni.navigateTo({
    url: queryString ? `${url}?${queryString}` : url
  })
}

/**
 * 页面跳转 - 关闭当前页面，跳转到应用内的某个页面
 * @param {string} url 页面路径
 * @param {Object} params 页面参数
 */
export function redirectTo(url, params = {}) {
  const queryString = formatParams(params)
  uni.redirectTo({
    url: queryString ? `${url}?${queryString}` : url
  })
}

/**
 * 页面跳转 - 关闭所有页面，打开到应用内的某个页面
 * @param {string} url 页面路径
 * @param {Object} params 页面参数
 */
export function reLaunch(url, params = {}) {
  const queryString = formatParams(params)
  const finalUrl = queryString ? `${url}?${queryString}` : url
  console.log('reLaunch 跳转到:', finalUrl)
  
  uni.reLaunch({
    url: finalUrl,
    success: () => {
      console.log('reLaunch 成功:', finalUrl)
    },
    fail: (err) => {
      console.error('reLaunch 失败:', err)
      // 尝试使用其他方法跳转
      console.log('尝试使用 redirectTo 跳转...')
      uni.redirectTo({
        url: finalUrl
      })
    }
  })
}

/**
 * 页面跳转 - 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
 * @param {string} url 页面路径
 */
export function switchTab(url) {
  uni.switchTab({
    url
  })
}

/**
 * 页面返回
 * @param {number} delta 返回的页面数
 */
export function navigateBack(delta = 1) {
  uni.navigateBack({
    delta
  })
}

/**
 * 格式化参数
 * @param {Object} params 参数对象
 * @returns {string} 格式化后的查询字符串
 */
function formatParams(params) {
  return Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&')
}
