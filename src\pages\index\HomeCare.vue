<template>
  <div class="home-care-panel">
    <div class="panel-header">
      <h3>居家看护</h3>
      <p class="panel-desc">专业的居家护理服务，让健康管理更贴心</p>
    </div>

    <!-- 服务概览 -->
    <div class="service-overview">
      <div class="overview-card" v-for="(item, index) in serviceOverview" :key="index">
        <div class="overview-icon">{{ item.icon }}</div>
        <div class="overview-content">
          <h5>{{ item.title }}</h5>
          <p class="overview-value">{{ item.value }}</p>
          <p class="overview-desc">{{ item.description }}</p>
        </div>
      </div>
    </div>

    <!-- 护理服务类型 -->
    <div class="care-services">
      <h4>护理服务</h4>
      
      <div class="service-tabs">
        <div 
          v-for="(service, index) in careServices" 
          :key="index"
          class="service-tab"
          :class="{ active: activeService === index }"
          @click="selectService(index)"
        >
          <span class="tab-icon">{{ service.icon }}</span>
          <span class="tab-name">{{ service.name }}</span>
        </div>
      </div>

      <div class="service-content" v-if="activeService !== -1">
        <div class="service-details">
          <h5>{{ currentService.title }}</h5>
          <p class="service-description">{{ currentService.description }}</p>
          
          <div class="service-features">
            <div class="feature-item" v-for="(feature, fIndex) in currentService.features" :key="fIndex">
              <div class="feature-icon">✓</div>
              <div class="feature-text">{{ feature }}</div>
            </div>
          </div>
          
          <div class="service-pricing">
            <span class="price">¥{{ currentService.price }}</span>
            <span class="price-unit">{{ currentService.unit }}</span>
          </div>
          
          <button class="btn-book-service" @click="bookService(currentService)">
            预约服务
          </button>
        </div>
      </div>
    </div>

    <!-- 护理人员 -->
    <div class="care-staff">
      <h4>专业护理团队</h4>
      
      <div class="staff-list">
        <div 
          v-for="(staff, sIndex) in careStaff" 
          :key="sIndex"
          class="staff-card"
        >
          <div class="staff-avatar">
            <img :src="staff.avatar" :alt="staff.name" />
            <div class="staff-status" :class="staff.status">
              {{ staff.status === 'available' ? '可预约' : '忙碌中' }}
            </div>
          </div>
          
          <div class="staff-info">
            <h6>{{ staff.name }}</h6>
            <p class="staff-title">{{ staff.title }}</p>
            <p class="staff-specialty">{{ staff.specialty }}</p>
            
            <div class="staff-stats">
              <div class="stat">
                <span class="stat-value">{{ staff.experience }}</span>
                <span class="stat-label">年经验</span>
              </div>
              <div class="stat">
                <span class="stat-value">{{ staff.rating }}</span>
                <span class="stat-label">评分</span>
              </div>
              <div class="stat">
                <span class="stat-value">{{ staff.cases }}</span>
                <span class="stat-label">服务案例</span>
              </div>
            </div>
            
            <div class="staff-tags">
              <span 
                v-for="(tag, tIndex) in staff.tags" 
                :key="tIndex"
                class="staff-tag"
              >
                {{ tag }}
              </span>
            </div>
          </div>
          
          <div class="staff-actions">
            <button 
              class="btn-contact" 
              @click="contactStaff(staff)"
              :disabled="staff.status !== 'available'"
            >
              联系护理师
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 护理计划 -->
    <div class="care-plan">
      <h4>我的护理计划</h4>
      
      <div class="plan-calendar">
        <div class="calendar-header">
          <button class="btn-nav" @click="previousWeek">‹</button>
          <span class="calendar-title">{{ currentWeekTitle }}</span>
          <button class="btn-nav" @click="nextWeek">›</button>
        </div>
        
        <div class="calendar-grid">
          <div class="calendar-day" v-for="(day, dIndex) in currentWeek" :key="dIndex">
            <div class="day-header">
              <span class="day-name">{{ day.name }}</span>
              <span class="day-date">{{ day.date }}</span>
            </div>
            
            <div class="day-appointments">
              <div 
                v-for="(appointment, aIndex) in day.appointments" 
                :key="aIndex"
                class="appointment-item"
                :class="appointment.type"
              >
                <div class="appointment-time">{{ appointment.time }}</div>
                <div class="appointment-service">{{ appointment.service }}</div>
                <div class="appointment-nurse">{{ appointment.nurse }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 健康监测 -->
    <div class="health-monitoring">
      <h4>健康监测</h4>
      
      <div class="monitoring-devices">
        <div 
          v-for="(device, dIndex) in monitoringDevices" 
          :key="dIndex"
          class="device-card"
          :class="{ connected: device.connected }"
        >
          <div class="device-icon">{{ device.icon }}</div>
          <div class="device-info">
            <h6>{{ device.name }}</h6>
            <p class="device-status">
              {{ device.connected ? '已连接' : '未连接' }}
            </p>
            <p class="device-last-reading" v-if="device.lastReading">
              最近读数: {{ device.lastReading }}
            </p>
          </div>
          <div class="device-actions">
            <button 
              class="btn-device"
              @click="toggleDevice(dIndex)"
            >
              {{ device.connected ? '断开' : '连接' }}
            </button>
          </div>
        </div>
      </div>
      
      <div class="monitoring-alerts" v-if="healthAlerts.length > 0">
        <h5>健康提醒</h5>
        <div class="alert-list">
          <div 
            v-for="(alert, aIndex) in healthAlerts" 
            :key="aIndex"
            class="alert-item"
            :class="alert.level"
          >
            <div class="alert-icon">{{ getAlertIcon(alert.level) }}</div>
            <div class="alert-content">
              <h6>{{ alert.title }}</h6>
              <p>{{ alert.message }}</p>
              <span class="alert-time">{{ alert.time }}</span>
            </div>
            <button class="btn-dismiss" @click="dismissAlert(aIndex)">×</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 紧急联系 -->
    <div class="emergency-contact">
      <h4>紧急联系</h4>
      
      <div class="emergency-buttons">
        <button class="btn-emergency" @click="callEmergency('120')">
          <span class="emergency-icon">🚑</span>
          <span class="emergency-text">急救 120</span>
        </button>
        
        <button class="btn-emergency" @click="callEmergency('nurse')">
          <span class="emergency-icon">👩‍⚕️</span>
          <span class="emergency-text">联系护理师</span>
        </button>
        
        <button class="btn-emergency" @click="callEmergency('family')">
          <span class="emergency-icon">👨‍👩‍👧‍👦</span>
          <span class="emergency-text">联系家属</span>
        </button>
      </div>
      
      <div class="emergency-contacts">
        <div 
          v-for="(contact, cIndex) in emergencyContacts" 
          :key="cIndex"
          class="contact-item"
        >
          <div class="contact-info">
            <span class="contact-name">{{ contact.name }}</span>
            <span class="contact-relation">{{ contact.relation }}</span>
          </div>
          <div class="contact-phone">{{ contact.phone }}</div>
          <button class="btn-call" @click="callContact(contact)">📞</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HomeCare',
  data() {
    return {
      activeService: 0,
      currentWeekOffset: 0,
      serviceOverview: [
        {
          icon: '👩‍⚕️',
          title: '专业护理师',
          value: '24小时',
          description: '全天候专业护理服务'
        },
        {
          icon: '📊',
          title: '健康监测',
          value: '实时',
          description: '智能设备实时监测'
        },
        {
          icon: '🏠',
          title: '居家服务',
          value: '上门',
          description: '专业护理师上门服务'
        },
        {
          icon: '📱',
          title: '远程咨询',
          value: '随时',
          description: '24小时在线医疗咨询'
        }
      ],
      careServices: [
        {
          name: '基础护理',
          icon: '🏥',
          title: '日常基础护理服务',
          description: '包括生活起居照料、个人卫生护理、用药提醒等基础护理服务',
          features: [
            '生活起居照料',
            '个人卫生护理',
            '用药提醒服务',
            '营养膳食指导',
            '健康状况观察'
          ],
          price: 150,
          unit: '/小时'
        },
        {
          name: '专业护理',
          icon: '💉',
          title: '专业医疗护理服务',
          description: '由专业护士提供的医疗护理服务，包括伤口护理、注射给药等',
          features: [
            '伤口换药护理',
            '注射给药服务',
            '导管护理',
            '康复训练指导',
            '病情监测记录'
          ],
          price: 200,
          unit: '/小时'
        },
        {
          name: '康复护理',
          icon: '🏃‍♂️',
          title: '康复训练护理服务',
          description: '专业的康复训练指导和护理，帮助患者恢复身体功能',
          features: [
            '功能训练指导',
            '运动康复护理',
            '物理治疗协助',
            '康复进度评估',
            '家庭康复指导'
          ],
          price: 180,
          unit: '/小时'
        }
      ],
      careStaff: [
        {
          name: '王护士长',
          title: '主管护师',
          specialty: '内科护理、老年护理',
          experience: 15,
          rating: 4.9,
          cases: 500,
          status: 'available',
          avatar: '/static/images/nurse1.jpg',
          tags: ['经验丰富', '专业认证', '温和耐心']
        },
        {
          name: '李护士',
          title: '护师',
          specialty: '外科护理、康复护理',
          experience: 8,
          rating: 4.8,
          cases: 300,
          status: 'busy',
          avatar: '/static/images/nurse2.jpg',
          tags: ['技术精湛', '责任心强', '沟通良好']
        }
      ],
      monitoringDevices: [
        {
          name: '血压监测仪',
          icon: '🩺',
          connected: true,
          lastReading: '120/80 mmHg'
        },
        {
          name: '血糖仪',
          icon: '🩸',
          connected: true,
          lastReading: '5.6 mmol/L'
        },
        {
          name: '心率监测器',
          icon: '❤️',
          connected: false,
          lastReading: null
        }
      ],
      healthAlerts: [
        {
          title: '血压偏高提醒',
          message: '您的血压读数为 140/90，建议及时咨询医生',
          level: 'warning',
          time: '2小时前'
        },
        {
          title: '用药提醒',
          message: '该服用降压药了，请按时用药',
          level: 'info',
          time: '30分钟前'
        }
      ],
      emergencyContacts: [
        {
          name: '张医生',
          relation: '主治医生',
          phone: '138-0000-1234'
        },
        {
          name: '李女士',
          relation: '女儿',
          phone: '139-0000-5678'
        }
      ]
    }
  },
  computed: {
    currentService() {
      return this.careServices[this.activeService] || {}
    },
    currentWeekTitle() {
      // 简化版，实际应该根据 currentWeekOffset 计算
      return '2024年1月第3周'
    },
    currentWeek() {
      // 简化版周历数据
      return [
        {
          name: '周一',
          date: '15',
          appointments: [
            {
              time: '09:00',
              service: '基础护理',
              nurse: '王护士长',
              type: 'basic'
            }
          ]
        },
        {
          name: '周二',
          date: '16',
          appointments: []
        },
        {
          name: '周三',
          date: '17',
          appointments: [
            {
              time: '14:00',
              service: '康复训练',
              nurse: '李护士',
              type: 'rehab'
            }
          ]
        },
        {
          name: '周四',
          date: '18',
          appointments: []
        },
        {
          name: '周五',
          date: '19',
          appointments: []
        },
        {
          name: '周六',
          date: '20',
          appointments: []
        },
        {
          name: '周日',
          date: '21',
          appointments: []
        }
      ]
    }
  },
  methods: {
    selectService(index) {
      this.activeService = index
    },
    bookService(service) {
      uni.showToast({
        title: `预约${service.title}`,
        icon: 'success'
      })
    },
    contactStaff(staff) {
      uni.showToast({
        title: `联系${staff.name}`,
        icon: 'none'
      })
    },
    previousWeek() {
      this.currentWeekOffset--
    },
    nextWeek() {
      this.currentWeekOffset++
    },
    toggleDevice(index) {
      this.monitoringDevices[index].connected = !this.monitoringDevices[index].connected
    },
    getAlertIcon(level) {
      const icons = {
        warning: '⚠️',
        info: 'ℹ️',
        error: '❌'
      }
      return icons[level] || 'ℹ️'
    },
    dismissAlert(index) {
      this.healthAlerts.splice(index, 1)
    },
    callEmergency(type) {
      const messages = {
        '120': '拨打急救电话 120',
        'nurse': '联系护理师',
        'family': '联系家属'
      }
      uni.showToast({
        title: messages[type],
        icon: 'none'
      })
    },
    callContact(contact) {
      uni.showToast({
        title: `拨打 ${contact.phone}`,
        icon: 'none'
      })
    }
  }
}
</script>

<style scoped>
.home-care-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(33,150,243,0.08);
}

.panel-header {
  margin-bottom: 24px;
  text-align: center;
}

.panel-header h3 {
  color: #1976d2;
  margin-bottom: 8px;
}

.panel-desc {
  color: #666;
  font-size: 14px;
}

.service-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.overview-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.overview-icon {
  font-size: 32px;
  margin-right: 16px;
}

.overview-content h5 {
  color: #333;
  margin-bottom: 4px;
}

.overview-value {
  font-size: 18px;
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 4px;
}

.overview-desc {
  font-size: 12px;
  color: #666;
}

.care-services {
  margin-bottom: 32px;
}

.service-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.service-tab {
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-tab:hover {
  background: #f5f5f5;
}

.service-tab.active {
  background: #e3f2fd;
  border-bottom: 2px solid #2196f3;
}

.service-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.service-details h5 {
  color: #1976d2;
  margin-bottom: 8px;
}

.service-description {
  color: #666;
  margin-bottom: 16px;
}

.service-features {
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.feature-icon {
  color: #4caf50;
  margin-right: 8px;
  font-weight: bold;
}

.service-pricing {
  margin-bottom: 20px;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #e91e63;
}

.price-unit {
  color: #666;
  margin-left: 4px;
}

.btn-book-service {
  background: #2196f3;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
}

.care-staff {
  margin-bottom: 32px;
}

.staff-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

.staff-card {
  display: flex;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.staff-avatar {
  position: relative;
  margin-right: 16px;
}

.staff-avatar img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.staff-status {
  position: absolute;
  bottom: -4px;
  right: -4px;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: bold;
}

.staff-status.available {
  background: #4caf50;
  color: white;
}

.staff-status.busy {
  background: #ff9800;
  color: white;
}

.staff-info {
  flex: 1;
}

.staff-info h6 {
  color: #333;
  margin-bottom: 4px;
}

.staff-title {
  color: #2196f3;
  font-size: 14px;
  margin-bottom: 4px;
}

.staff-specialty {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.staff-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-weight: bold;
  color: #2196f3;
}

.stat-label {
  font-size: 10px;
  color: #666;
}

.staff-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.staff-tag {
  font-size: 10px;
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 8px;
}

.staff-actions {
  margin-left: 16px;
}

.btn-contact {
  padding: 8px 16px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-contact:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.care-plan {
  margin-bottom: 32px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.btn-nav {
  background: none;
  border: 1px solid #ddd;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.calendar-title {
  font-weight: bold;
  color: #333;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.calendar-day {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  min-height: 100px;
}

.day-header {
  background: #f8f9fa;
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
}

.day-name {
  display: block;
  font-size: 12px;
  color: #666;
}

.day-date {
  display: block;
  font-weight: bold;
  color: #333;
}

.day-appointments {
  padding: 4px;
}

.appointment-item {
  background: #e3f2fd;
  border-radius: 4px;
  padding: 4px;
  margin-bottom: 4px;
  font-size: 10px;
}

.appointment-item.basic {
  background: #e8f5e8;
}

.appointment-item.rehab {
  background: #fff3e0;
}

.appointment-time {
  font-weight: bold;
  color: #333;
}

.appointment-service {
  color: #666;
}

.appointment-nurse {
  color: #888;
}

.health-monitoring {
  margin-bottom: 32px;
}

.monitoring-devices {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.device-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.device-card.connected {
  border-color: #4caf50;
  background: #f1f8e9;
}

.device-icon {
  font-size: 24px;
  margin-right: 16px;
}

.device-info {
  flex: 1;
}

.device-info h6 {
  color: #333;
  margin-bottom: 4px;
}

.device-status {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.device-last-reading {
  font-size: 12px;
  color: #2196f3;
  font-weight: bold;
}

.btn-device {
  padding: 6px 12px;
  border: 1px solid #2196f3;
  background: white;
  color: #2196f3;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.monitoring-alerts {
  background: #fff3cd;
  border-radius: 8px;
  padding: 16px;
}

.alert-list {
  margin-top: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
}

.alert-item.warning {
  background: #fff3cd;
  border-left: 4px solid #ffc107;
}

.alert-item.info {
  background: #d1ecf1;
  border-left: 4px solid #17a2b8;
}

.alert-icon {
  margin-right: 12px;
  font-size: 18px;
}

.alert-content {
  flex: 1;
}

.alert-content h6 {
  color: #333;
  margin-bottom: 4px;
}

.alert-content p {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: #888;
}

.btn-dismiss {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
}

.emergency-contact {
  background: #ffebee;
  border-radius: 8px;
  padding: 20px;
}

.emergency-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  justify-content: center;
}

.btn-emergency {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  min-width: 100px;
}

.emergency-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.emergency-text {
  font-size: 12px;
  font-weight: bold;
}

.emergency-contacts {
  border-top: 1px solid #e0e0e0;
  padding-top: 16px;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-weight: bold;
  color: #333;
  margin-right: 8px;
}

.contact-relation {
  font-size: 12px;
  color: #666;
}

.contact-phone {
  color: #2196f3;
  margin-right: 12px;
}

.btn-call {
  background: #4caf50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
