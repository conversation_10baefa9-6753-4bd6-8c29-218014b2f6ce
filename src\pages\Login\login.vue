<template>
  <view class="login-page">
    <view class="login-container">
      <view class="login-content">
        <image class="logo" src="/static/logo.png" mode="widthFix" v-if="showLogo" />
        <form @submit="handleLogin">
          <view class="input-group">
            <input
              class="input"
              type="text"
              v-model="phoneNumber"
              placeholder="请输入手机号"
              maxlength="11"
              @blur="validatePhone"
              :class="{ error: phoneError }"
            />
            <text v-if="phoneError" class="error-text">{{ phoneError }}</text>
          </view>
          <view class="input-group">
            <input
              class="input"
              type="password"
              v-model="password"
              placeholder="请输入密码"
            />
          </view>
          <button
            class="login-btn"
            :loading="loading"
            :disabled="loading"
            form-type="submit"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>
          <text v-if="loginError" class="error-text">{{ loginError }}</text>
        </form>
      </view>
    </view>
  </view>
</template>

<script>
import { login } from '@/api/user'
import { setItem, getItem, removeItem } from '@/utils/storage'
import { reLaunch, ROUTES } from '@/utils/router'

export default {
  data() {
    console.log('[Login] data() 初始化');
    return {
      phoneNumber: '',
      password: '',
      remember: false,
      loading: false,
      phoneError: '',
      loginError: '',
      showLogo: true,
      loginUserName: ''
    }
  },
  onLoad() {
    console.log('[Login] onLoad 触发');
    // 自动填充记住的手机号
    const remembered = getItem('rememberPhone')
    console.log('[Login] 读取 rememberPhone:', remembered)
    if (remembered) {
      this.phoneNumber = remembered
      this.remember = true
    }
    // 已登录自动跳转
    const token = getItem('token')
    console.log('[Login] 读取 token:', token)
    if (token) {
      console.log('[Login] 检测到 token，准备跳转首页');
      reLaunch(ROUTES.INDEX)
    }
  },
  created() {
    // 兼容 onLoad
    let userInfo = getItem('userInfo')
    if (userInfo && userInfo.userName) {
      this.loginUserName = userInfo.userName
    } else {
      this.loginUserName = '未登录'
    }
  },
  mounted() {
    console.log('[Login] mounted 生命周期钩子触发');
  },
  methods: {
    validatePhone() {
      console.log('[Login] validatePhone 调用，phoneNumber:', this.phoneNumber)
      const reg = /^1[3-9]\d{9}$/
      if (!reg.test(this.phoneNumber)) {
        this.phoneError = '请输入有效的手机号'
        return false
      }
      this.phoneError = ''
      return true
    },
    async handleLogin(e) {
      console.log('[Login] handleLogin 调用，phoneNumber:', this.phoneNumber, 'password:', this.password)
      this.loginError = ''
      if (!this.validatePhone()) return
      if (!this.password) {
        this.loginError = '请输入密码'
        return
      }
      this.loading = true
      try {
        console.log('[Login] 开始登录请求...')
        const res = await login({
          phoneNumber: this.phoneNumber,
          password: this.password
        })
        console.log('[Login] 登录响应:', res)
        if (res.data.token) {
          console.log('[Login] 登录成功，token:', res.data.token)
          setItem('token', res.data.token)
          setItem('userInfo', res.data.userInfo)
          if (this.remember) setItem('rememberPhone', this.phoneNumber)
          else removeItem('rememberPhone')
          console.log('[Login] 登录成功，准备跳转到首页...')
          uni.reLaunch({
            url: '/pages/index/index',
            success: () => {
              console.log('[Login] 跳转首页成功')
            },
            fail: (err) => {
              console.error('[Login] 跳转首页失败:', err)
              this.loginError = '页面跳转失败: ' + JSON.stringify(err)
            }
          })
          setTimeout(() => {
            console.log('[Login] 尝试延迟跳转...')
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }, 1000)
        } else {
          this.loginError = res.message || '登录失败'
          console.error('[Login] 登录失败:', this.loginError)
        }
      } catch (err) {
        console.error('[Login] 登录异常:', err)
        this.loginError = err.message || '登录失败，请重试'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;   /* 垂直居中 */
  align-items: center;       /* 水平居中 */
  background: url('/static/images/login-bg.jpg') center center / cover no-repeat;
}
.platform-title {
  width: 100vw;
  text-align: center;
  font-size: 80rpx;
  font-weight: bold;
  color: #00796b;
  margin-top: 20rpx;
  margin-bottom: 0;
  letter-spacing: 4rpx;
  flex-shrink: 0;
}
.login-container {
  flex: 1;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0;
  min-height: 0;
  margin-top: 0 !important;
  padding-top: 0 !important;
}
.login-content {
  width: 100%;
  max-width: 360px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20rpx;
  margin-top: 0;
  min-height: 0;
}
.logo {
  width: 120rpx;
  margin-bottom: 40rpx;
}
.title {
  font-size: 40rpx;
  color: #009688;
  margin-bottom: 40rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
  text-align: center;
}
.input-group {
  width: 100%;
  margin-bottom: 32rpx;
  position: relative;
}
.input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid #2196f3;
  border-radius: 24px;
  font-size: 32rpx;
  background: rgba(255,255,255,0.85);
  box-shadow: 0 2rpx 8rpx rgba(33,150,243,0.08);
  transition: border-color 0.2s, box-shadow 0.2s;
  color: #222;
  margin-bottom: 24px;
}
.input:focus {
  border-color: #1565c0;
  box-shadow: 0 0 0 3rpx #2196f355;
}
.input.error {
  border-color: #e57373;
  background: rgba(255,255,255,0.95);
}
.error-text {
  color: #e57373;
  font-size: 28rpx;
  margin-top: 8rpx;
  display: block;
}
.options {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  font-size: 28rpx;
}
.links navigator {
  color: #009688;
  text-decoration: none;
  font-size: 28rpx;
  margin-left: 12rpx;
}
.login-btn {
  width: 60%;
  margin-left: 20%;
  margin-right: 0;
  background: linear-gradient(90deg, #2196f3 0%, #1565c0 100%);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 20px;
  font-weight: bold;
  height: 48px;
  margin-top: 24px;
  box-shadow: 0 4px 16px rgba(33,150,243,0.25);
  transition: background 0.2s, box-shadow 0.2s;
  letter-spacing: 2px;
}
.login-btn:active {
  background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%);
}
@media (max-width: 480px) {
  .login-container {
    padding: 20rpx 4rpx;
    margin-top: 0;
  }
  .login-content {
    margin-top: 0;
  }
  .platform-title {
    font-size: 48rpx;
    margin-top: 8rpx;
    margin-bottom: 0;
  }
}
/* 移除body等overflow，防止全局滚动条 */
body, html, :root, page {
  height: 100vh;
  margin: 0;
  padding: 0;
}
</style> 
