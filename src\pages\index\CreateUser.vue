<template>
  <div class="create-user-modal">
    <div class="modal-overlay" @click="closeModal"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h3>用户建档</h3>
        <button class="close-btn" @click="closeModal">×</button>
      </div>
      
      <div class="modal-body">
        <form @submit.prevent="submitForm">
          <!-- 基本信息 -->
          <div class="form-section">
            <h4>基本信息</h4>
            <div class="form-row">
              <label>真实姓名 *</label>
              <input type="text" v-model="formData.realName" required placeholder="请输入真实姓名" />
            </div>
            <div class="form-row">
              <label>手机号 *</label>
              <input type="tel" v-model="formData.phoneNumber" required placeholder="请输入手机号" />
            </div>
            <div class="form-row">
              <label>身份证号 *</label>
              <input type="text" v-model="formData.idCard" required placeholder="请输入身份证号" />
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>年龄 *</label>
                <input type="number" v-model="formData.age" required placeholder="年龄" min="0" max="150" />
              </div>
              <div class="form-group">
                <label>性别 *</label>
                <select v-model="formData.gender" required>
                  <option value="">请选择</option>
                  <option value="男">男</option>
                  <option value="女">女</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <label>家庭住址 *</label>
              <input type="text" v-model="formData.address" required placeholder="请输入详细地址" />
            </div>
          </div>

          <!-- 监护人信息 -->
          <div class="form-section">
            <h4>监护人信息</h4>
            <div class="form-row">
              <label>监护人姓名</label>
              <input type="text" v-model="formData.guardianName" placeholder="请输入监护人姓名" />
            </div>
            <div class="form-row">
              <label>监护人手机号</label>
              <input type="tel" v-model="formData.guardianPhone" placeholder="请输入监护人手机号" />
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>监护人性别</label>
                <select v-model="formData.guardianGender">
                  <option value="">请选择</option>
                  <option value="男">男</option>
                  <option value="女">女</option>
                </select>
              </div>
              <div class="form-group">
                <label>与监护人关系</label>
                <input type="text" v-model="formData.guardianRelation" placeholder="如：父子、母女等" />
              </div>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="closeModal">取消</button>
            <button type="submit" class="submit-btn" :disabled="isSubmitting">
              {{ isSubmitting ? '创建中...' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { registerUser } from '@/api/user'

export default {
  name: 'CreateUser',
  data() {
    return {
      isSubmitting: false,
      formData: {
        realName: '',
        phoneNumber: '',
        idCard: '',
        age: '',
        gender: '',
        address: '',
        guardianName: '',
        guardianPhone: '',
        guardianGender: '',
        guardianRelation: ''
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('close')
    },
    
    async submitForm() {
      // 验证必填字段
      if (!this.validateForm()) {
        return
      }
      
      this.isSubmitting = true
      
      try {
        const response = await registerUser(this.formData)
        
        if (response.state === 0) {
          uni.showToast({
            title: '建档成功',
            icon: 'success'
          })
          this.$emit('success', response.data)
          this.closeModal()
        } else {
          uni.showToast({
            title: response.message || '建档失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('建档失败:', error)
        uni.showToast({
          title: '建档失败，请重试',
          icon: 'none'
        })
      } finally {
        this.isSubmitting = false
      }
    },
    
    validateForm() {
      const required = ['realName', 'phoneNumber', 'idCard', 'age', 'gender', 'address']
      
      for (let field of required) {
        if (!this.formData[field]) {
          uni.showToast({
            title: `请填写${this.getFieldName(field)}`,
            icon: 'none'
          })
          return false
        }
      }
      
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(this.formData.phoneNumber)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }
      
      // 验证身份证号格式（简单验证）
      const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
      if (!idCardRegex.test(this.formData.idCard)) {
        uni.showToast({
          title: '请输入正确的身份证号',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    getFieldName(field) {
      const fieldNames = {
        realName: '真实姓名',
        phoneNumber: '手机号',
        idCard: '身份证号',
        age: '年龄',
        gender: '性别',
        address: '家庭住址'
      }
      return fieldNames[field] || field
    }
  }
}
</script>

<style scoped>
.create-user-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #1976d2;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #f44336;
}

.modal-body {
  padding: 20px;
}

.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #1976d2;
  font-size: 16px;
  border-bottom: 2px solid #e3f2fd;
  padding-bottom: 8px;
}

.form-row {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

.form-row label {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.form-row input, .form-row select {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-row input:focus, .form-row select:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.form-row:has(.form-group) {
  flex-direction: row;
  gap: 16px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.cancel-btn, .submit-btn {
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

.submit-btn {
  background: #4caf50;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #45a049;
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
