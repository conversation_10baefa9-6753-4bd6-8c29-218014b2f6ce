# 依赖目录
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock
pnpm-lock.yaml

# 编译输出
/dist/
/unpackage/
/lib/
/build/

# 本地环境文件
.env.local
.env.*.local

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
Thumbs.db

# uni-app 特定文件
.hbuilderx/
.history/

# 测试覆盖率报告
/coverage/

# 临时文件
.temp
.tmp
.cache

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 调试文件
debug.log
