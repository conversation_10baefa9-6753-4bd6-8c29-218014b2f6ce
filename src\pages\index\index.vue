<template>

<!-- 这是一个代理商登录的页面，用户类型有三种：ADMIN, AGENT, USER -->
<!-- 健康档案表单区域为固定 -->
  <div class="index-bg">
    <!-- 顶部栏 -->
    <div class="top-bar">
      <span class="platform-title">宓元堂荣护智能康养平台</span>
      <div class="user-info-wrapper" @mouseenter="showUserCard = true" @mouseleave="showUserCard = false">
        <img class="user-avatar" src="/static/images/default_handsome.jpg" alt="用户头像" />
        <span class="user-name">{{ loginUserName }}</span>
        <div class="user-card" v-if="showUserCard">
          <div class="user-card-header">
            <img class="user-card-avatar" src="/static/images/default_handsome.jpg" alt="用户头像" />
            <div class="user-card-info">
              <div class="user-card-name">{{ loginUserName }}</div>
              <div class="user-card-id">账号ID: {{ userId }}</div>
            </div>
          </div>
          <button class="user-card-logout" @click="handleLogout">退出登录</button>
        </div>
      </div>
    </div>
    <div class="main-layout">
      <!-- 左侧导航栏 -->
      <div class="sidebar">
        <div v-for="(tab, index) in tabs" :key="index" class="menu-item" :class="{ active: activeTab === index }" @click="switchTab(index)">
          {{ tab.name }}
        </div>
      </div>
      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 固定显示的健康档案表单 -->
        <div class="profile-form">
          <div class="form-row">
            <label>用户名：</label><input type="text" value="张天伦" readonly />
            <label>年龄：</label><input type="text" value="62" readonly />
            <label>性别：</label><input type="text" value="男" readonly />
            <label>联系人：</label><input type="text" value="张李天" readonly />
            <label>电话：</label><input type="text" value="13988888888" readonly />
          </div>
          <div class="form-row">
            <label>电话：</label><input type="text" value="13900000000" readonly />
            <label>住址：</label><input type="text" value="北京市朝阳区幸福家园8号楼8单元808" readonly style="width:320px;" />
          </div>
        </div>
        <!-- 随tab切换的内容 -->
        <div class="tab-content">
          <component :is="currentTabComponent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { redirectTo, ROUTES } from '@/utils/router'
import { logout } from '@/api/user'
import { getItem, removeItem } from '@/utils/storage'
import store from '@/store'
import HealthCheck from './HealthCheck.vue'
import HealthReport from './HealthReport.vue'
import HealthAssessment from './HealthAssessment.vue'
import HealthPlan from './HealthPlan.vue'
import RehabCenter from './RehabCenter.vue'
import HomeCare from './HomeCare.vue'

export default {
  name: 'Index',
  components: {
    HealthCheck,
    HealthReport,
    HealthAssessment,
    HealthPlan,
    RehabCenter,
    HomeCare
  },
  data() {
    return {
      activeTab: 0,
      tabs: [
        { name: '健康体检', component: 'HealthCheck' },
        { name: '健康报告', component: 'HealthReport' },
        { name: '健康评估', component: 'HealthAssessment' },
        { name: '调养方案', component: 'HealthPlan' },
        { name: '康复中心', component: 'RehabCenter' },
        { name: '居家看护', component: 'HomeCare' }
      ],
      loginUserName: '',
      showUserCard: false,
      userId: ''
    };
  },
  computed: {
    currentTabComponent() {
      return this.tabs[this.activeTab].component;
    }
  },
  created() {
    const userInfo = getItem('userInfo');
    if (userInfo) {
      this.loginUserName = userInfo.realName || '未登录';
      this.userId = userInfo.id || '';
    } else {
      this.loginUserName = '未登录';
      this.userId = '';
    }
  },
  onLoad() {
    // 检查登录状态
    const token = uni.getStorageSync('token');
    if (!token) {
      redirectTo(ROUTES.LOGIN);
    }
  },
  methods: {
    switchTab(index) {
      this.activeTab = index;
    },
    async handleLogout() {
      try {
        const token = getItem('token');
        const userInfo = getItem('userInfo');
        const userId = userInfo && userInfo.id ? userInfo.id : '';
        if (!token || !userId) {
          // 本地无登录信息，直接清理并跳转
          store.commit('user/CLEAR_USER');
          removeItem('token');
          removeItem('userInfo');
          redirectTo(ROUTES.LOGIN);
          return;
        }
        await logout(token, userId);
      } catch (e) {
        // 可根据需要提示错误
        console.error('退出失败', e);
      } finally {
        store.commit('user/CLEAR_USER');
        removeItem('token');
        removeItem('userInfo');
        redirectTo(ROUTES.LOGIN);
      }
    }
  }
}
</script>

<style>
.index-bg {
  min-height: 100vh;
  background: url('/static/images/index-bg.jpg') center/cover no-repeat;
  position: relative;
}
.top-bar {
  height: 64px;
  background: rgba(25, 118, 210, 0.8);
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 32px;
  justify-content: flex-start;
  gap: 24px;
}
.platform-title {
  font-size: 28px;
  font-weight: bold;
  color: #b9cbd8;
}
.main-layout {
  display: flex;
  height: calc(100vh - 64px);
}
.sidebar {
  width: 250px;
  background: rgba(0, 60, 120, 0.5);
  color: #fff;
  padding-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
}
.menu-item {
  width: 180px;
  margin: 0;
  padding: 0;
  border-radius: 22px;
  background: linear-gradient(90deg, #4fc3f7 0%, #1976d2 100%);
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  height: 44px;
  line-height: 44px;
  box-shadow: 0 2px 8px rgba(33,150,243,0.18);
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  outline: none;
}
.menu-item.active {
  background: #fff;
  color: #1976d2;
  box-shadow: 0 2px 12px rgba(33,150,243,0.25);
  border: 2px solid #1976d2;
}
.main-content {
  flex: 1;
  padding: 32px;
  border-radius: 12px;
  margin: 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.profile-form {
  background: #e3f2fd;
  border-radius: 8px;
  padding: 16px 24px;
  margin-bottom: 16px;
}
.form-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}
.form-row label {
  font-weight: bold;
  color: #1976d2;
  margin-right: 4px;
}
.form-row input {
  border: 1px solid #90caf9;
  border-radius: 4px;
  padding: 2px 8px;
  background: #fff;
  color: #333;
  width: 100px;
}
.health-table {
  /* background: #e3f2fd; */
  border-radius: 8px;
  padding: 16px 24px;
  overflow-x: auto;
}
.health-table table {
  width: 100%;
  border-collapse: collapse;
  /* background: #fff; */
}
.health-table th, .health-table td {
  border: 1px solid #90caf9;
  padding: 6px 10px;
  text-align: center;
  font-size: 14px;
}
.health-table th {
  background: #1976d2;
  color: #fff;
}
.user-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  margin-left: 20px;
  border: 2px solid #fff;
  background: #eee;
  display: inline-block;
  vertical-align: middle;
}
.user-name {
  margin-left: 8px;
  color: #fff;
  font-size: 16px;
  vertical-align: middle;
  display: inline-block;
  font-weight: bold;
}
.user-info-wrapper {
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  margin-left: auto;
}
.user-card {
  position: absolute;
  top: 40px;
  right: 0;
  width: 260px;
  background: #fff;
  box-shadow: 0 4px 24px rgba(0,0,0,0.15);
  border-radius: 8px;
  padding: 16px;
  z-index: 100;
  color: #333;
}
.user-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.user-card-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 12px;
  border: 1px solid #eee;
}
.user-card-info {
  flex: 1;
}
.user-card-name {
  font-weight: bold;
  font-size: 18px;
  color: #1976d2;
}
.user-card-id {
  font-size: 13px;
  color: #888;
  margin-top: 2px;
}
.user-card-logout {
  width: 60%;
  background: #e57373;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 4px 0;
  font-size: 13px;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.user-card-logout:hover {
  background: #d32f2f;
}
.tab-content {
  flex: 1;
  overflow-y: auto;
}
</style> 
