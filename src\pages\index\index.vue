<template>

  <div class="index-bg">
    <!-- 顶部栏 -->
    <div class="top-bar">
      <span class="platform-title">宓元堂荣护智能康养平台</span>
      <div class="user-info-wrapper" @mouseenter="showUserCard = true" @mouseleave="showUserCard = false">
        <img class="user-avatar" src="/static/images/default_handsome.jpg" alt="用户头像" />
        <span class="user-name">{{ loginUserName }}</span>
        <div class="user-card" v-if="showUserCard">
          <div class="user-card-header">
            <img class="user-card-avatar" src="/static/images/default_handsome.jpg" alt="用户头像" />
            <div class="user-card-info">
              <div class="user-card-name">{{ loginUserName }}</div>
              <div class="user-card-id">账号ID: {{ userId }}</div>
            </div>
          </div>
          <button class="user-card-logout" @click="handleLogout">退出登录</button>
        </div>
      </div>
    </div>
    <div class="main-layout">
      <!-- 左侧导航栏 -->
      <div class="sidebar">
        <div v-for="(tab, index) in tabs" :key="index" class="menu-item" :class="{ active: activeTab === index }" @click="switchTab(index)">
          {{ tab.name }}
        </div>
      </div>
      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 固定显示的健康档案表单 -->
        <div class="profile-section">
          <div class="section-with-label">
            <div class="vertical-label">健康档案</div>
            <div class="profile-form">
              <!-- 用户查询区域 -->
              <div class="user-search-row">
                <label>手机号查询：</label>
                <input
                  type="text"
                  v-model="searchPhone"
                  placeholder="请输入用户手机号"
                  @keyup.enter="searchUser"
                  @input="onSearchInput"
                  class="search-input"
                />
                <button @click="searchUser" class="search-btn" :disabled="!searchPhone || isSearching">
                  {{ isSearching ? '查询中...' : '查询' }}
                </button>
                <button @click="clearUser" class="clear-btn" v-if="currentUser.phoneNumber">清空</button>
                <button @click="createUser" class="create-btn" :disabled="isCreating">
                  {{ isCreating ? '建档中...' : '建档' }}
                </button>
              </div>

              <!-- 用户下拉列表 -->
              <div class="user-dropdown" v-if="userList.length > 0 && showDropdown">
                <div class="dropdown-header">
                  <span>找到 {{ userList.length }} 个用户</span>
                  <button @click="closeDropdown" class="close-dropdown">×</button>
                </div>
                <div class="user-list">
                  <div
                    v-for="user in userList"
                    :key="user.id"
                    class="user-item"
                    @click="selectUser(user)"
                  >
                    <div class="user-basic">
                      <span class="user-name">{{ user.realName }}</span>
                      <span class="user-phone">{{ user.phoneNumber }}</span>
                    </div>
                    <div class="user-details">
                      <span class="user-age">{{ user.age }}岁</span>
                      <span class="user-gender">{{ user.gender }}</span>
                      <span class="user-agent">{{ user.agentName }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 用户信息显示区域 -->
              <div v-if="currentUser.phoneNumber" class="user-info-area">
                <!-- 第一行：姓名 -->
                <div class="form-row">
                  <label>姓名：</label><input type="text" :value="currentUser.realName" readonly class="name-input" />
                </div>
                <!-- 第二行：年龄、性别 -->
                <div class="form-row age-gender-row">
                  <div class="age-section">
                    <label>年龄：</label><input type="text" :value="currentUser.age + ' 周岁'" readonly class="age-input" />
                  </div>
                  <div class="gender-section">
                    <label>性别：</label><input type="text" :value="currentUser.gender" readonly class="gender-input" />
                  </div>
                </div>
                <!-- 第三行：住址 -->
                <div class="form-row">
                  <label>住址：</label><input type="text" :value="currentUser.address" readonly class="address-input" />
                </div>
              </div>

              <!-- 无用户时的提示 -->
              <div v-else class="no-user-tip">
                <p>请输入手机号查询用户信息</p>
              </div>
            </div>
          </div>
        </div>
        <!-- 随tab切换的内容 -->
        <div class="content-section" :class="{ 'full-height': isHealthProfileTab }">
          <div class="section-with-label">
            <div class="vertical-label">{{ tabs[activeTab].name }}</div>
            <div class="tab-content">
              <component :is="currentTabComponent" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 建档弹窗 -->
    <CreateUser v-if="showCreateModal" @close="closeCreateModal" @success="onCreateSuccess" />
  </div>
</template>

<script>
import { redirectTo, ROUTES } from '@/utils/router'
import { logout, getUsersByAgent } from '@/api/user'
import { getItem, removeItem } from '@/utils/storage'
import store from '@/store'
import HealthProfile from './HealthProfile.vue'
import HealthCheck from './HealthCheck.vue'
import HealthReport from './HealthReport.vue'
import HealthAssessment from './HealthAssessment.vue'
import HealthPlan from './HealthPlan.vue'
import RehabCenter from './RehabCenter.vue'
import HomeCare from './HomeCare.vue'
import CreateUser from './CreateUser.vue'

export default {
  name: 'Index',
  components: {
    HealthProfile,
    HealthCheck,
    HealthReport,
    HealthAssessment,
    HealthPlan,
    RehabCenter,
    HomeCare,
    CreateUser
  },
  data() {
    return {
      activeTab: 0,
      tabs: [
        { name: '健康档案', component: 'HealthProfile' },
        { name: '健康体检', component: 'HealthCheck' },
        { name: '健康报告', component: 'HealthReport' },
        { name: '健康评估', component: 'HealthAssessment' },
        { name: '调养方案', component: 'HealthPlan' },
        { name: '康复中心', component: 'RehabCenter' },
        { name: '居家看护', component: 'HomeCare' }
      ],
      loginUserName: '',
      showUserCard: false,
      userId: '',
      // 用户查询相关
      searchPhone: '',
      currentUser: {
        id: '',
        realName: '',
        age: '',
        gender: '',
        phoneNumber: '',
        address: '',
        guardianName: '',
        guardianPhone: '',
        guardianGender: '',
        guardianRelation: '',
        agentName: '',
        userType: '',
        status: '',
        idCard: '',
        username: ''
      },
      userList: [],
      showDropdown: false,
      isSearching: false,
      isCreating: false,
      showCreateModal: false
    };
  },
  computed: {
    currentTabComponent() {
      return this.tabs[this.activeTab].component;
    },
    isHealthProfileTab() {
      return this.tabs[this.activeTab].component === 'HealthProfile';
    }
  },
  watch: {
    currentUser: {
      handler(newVal) {
        console.log('currentUser 数据变化:', newVal);
      },
      deep: true
    }
  },
  created() {
    const userInfo = getItem('userInfo');
    if (userInfo) {
      this.loginUserName = userInfo.realName || '未登录';
      this.userId = userInfo.id || '';
    } else {
      this.loginUserName = '未登录';
      this.userId = '';
    }
  },
  onLoad() {
    // 检查登录状态
    const token = uni.getStorageSync('token');
    if (!token) {
      redirectTo(ROUTES.LOGIN);
    }
  },
  methods: {
    switchTab(index) {
      this.activeTab = index;
    },
    async handleLogout() {
      try {
        const token = getItem('token');
        const userInfo = getItem('userInfo');
        const userId = userInfo && userInfo.id ? userInfo.id : '';
        if (!token || !userId) {
          // 本地无登录信息，直接清理并跳转
          store.commit('user/CLEAR_USER');
          removeItem('token');
          removeItem('userInfo');
          redirectTo(ROUTES.LOGIN);
          return;
        }
        await logout(token, userId);
      } catch (e) {
        // 可根据需要提示错误
        console.error('退出失败', e);
      } finally {
        store.commit('user/CLEAR_USER');
        removeItem('token');
        removeItem('userInfo');
        redirectTo(ROUTES.LOGIN);
      }
    },

    // 根据手机号查询用户
    async searchUser() {
      if (!this.searchPhone || this.searchPhone.trim() === '') {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
        return;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.searchPhone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return;
      }

      this.isSearching = true;
      this.userList = [];
      this.showDropdown = false;

      try {
        const userInfo = getItem('userInfo');
        const agentId = userInfo?.id;
        const currentUserType = userInfo?.userType || 'AGENT';

        const response = await getUsersByAgent({
          agentId,
          phoneNumber: this.searchPhone,
          currentUserId: agentId,
          currentUserType
        });

        console.log('API 响应数据:', response);

        if (response.state === 1 && response.data && response.data.list) {
          this.userList = response.data.list;

          console.log('用户列表:', this.userList);

          if (this.userList.length === 0) {
            uni.showToast({
              title: '未找到该用户',
              icon: 'none'
            });
          } else if (this.userList.length === 1) {
            // 只有一个用户，直接选中
            console.log('找到一个用户，直接选中:', this.userList[0]);
            this.selectUser(this.userList[0]);
          } else {
            // 多个用户，显示下拉列表
            console.log('找到多个用户，显示下拉列表');
            this.showDropdown = true;
          }
        } else {
          console.error('API 响应错误:', response);
          uni.showToast({
            title: response.message || '查询失败',
            icon: 'none'
          });
        }

      } catch (error) {
        console.error('查询用户失败:', error);
        uni.showToast({
          title: '查询失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isSearching = false;
      }
    },


    // 选择用户
    selectUser(user) {
      console.log('选择的用户数据:', user);

      // 确保数据正确映射
      this.currentUser = {
        id: user.id || '',
        realName: user.realName || '',
        age: user.age || '',
        gender: user.gender || '',
        phoneNumber: user.phoneNumber || '',
        address: user.address || '',
        guardianName: user.guardianName || '',
        guardianPhone: user.guardianPhone || '',
        guardianGender: user.guardianGender || '',
        guardianRelation: user.guardianRelation || '',
        agentName: user.agentName || '',
        userType: user.userType || '',
        status: user.status || '',
        idCard: user.idCard || '',
        username: user.username || ''
      };

      this.showDropdown = false;

      console.log('设置后的 currentUser:', this.currentUser);

      uni.showToast({
        title: '用户信息已加载',
        icon: 'success'
      });
    },

    // 关闭下拉列表
    closeDropdown() {
      this.showDropdown = false;
    },

    // 输入框输入事件
    onSearchInput() {
      // 输入时关闭下拉列表
      this.showDropdown = false;
    },

    // 获取用户状态显示文本
    getUserStatus(status) {
      const statusMap = {
        'NORMAL': '正常',
        'DISABLED': '禁用',
        'PENDING': '待审核'
      };
      return statusMap[status] || status;
    },

    // 清空用户信息
    clearUser() {
      this.searchPhone = '';
      this.userList = [];
      this.showDropdown = false;
      this.currentUser = {
        id: '',
        realName: '',
        age: '',
        gender: '',
        phoneNumber: '',
        address: '',
        guardianName: '',
        guardianPhone: '',
        guardianGender: '',
        guardianRelation: '',
        agentName: '',
        userType: '',
        status: '',
        idCard: '',
        username: ''
      };

      console.log('用户信息已清空');
    },

    // 建档功能
    createUser() {
      this.showCreateModal = true;
    },

    // 关闭建档弹窗
    closeCreateModal() {
      this.showCreateModal = false;
    },

    // 建档成功回调
    onCreateSuccess(userData) {
      console.log('建档成功:', userData);
      // 可以刷新用户列表或执行其他操作
      uni.showToast({
        title: '建档成功',
        icon: 'success'
      });
    }
  }
}
</script>

<style>
.index-bg {
  height: 100vh;
  background: url('/static/images/index-bg.jpg') center/cover no-repeat;
  position: relative;
  overflow: hidden;
}
.top-bar {
  height: 64px;
  background: rgba(25, 118, 210, 0.8);
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 32px;
  justify-content: flex-start;
  gap: 24px;
}
.platform-title {
  font-size: 28px;
  font-weight: bold;
  color: #b9cbd8;
}
.main-layout {
  display: flex;
  height: calc(100vh - 64px);
}
.sidebar {
  width: 250px;
  background: rgba(13, 71, 161, 0.6);
  color: #fff;
  padding-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
}
.menu-item {
  width: 180px;
  margin: 0;
  padding: 0;
  border-radius: 22px;
  background: linear-gradient(90deg, #4fc3f7 0%, #1976d2 100%);
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  height: 44px;
  line-height: 44px;
  box-shadow: 0 2px 8px rgba(33,150,243,0.18);
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  outline: none;
}
.menu-item.active {
  background: #fff;
  color: #1976d2;
  box-shadow: 0 2px 12px rgba(33,150,243,0.25);
  border: 2px solid #1976d2;
}
.main-content {
  flex: 1;
  padding: 32px 32px 16px 32px; /* 减少底部内边距 */
  border-radius: 12px;
  margin: 32px 32px 16px 32px; /* 减少底部外边距 */
  display: flex;
  flex-direction: column;
  gap: 24px;
  transition: all 0.3s ease-in-out;
  overflow-y: auto;
  max-height: calc(100vh - 64px - 32px); /* 限制最大高度 */
}
.profile-section {
  margin-bottom: 16px;
  transition: all 0.3s ease-in-out;
}

.content-section {
  flex: 1;
}

.content-section.full-height {
  /* 当显示健康档案页面时，占用更多空间 */
  flex: 1;
  height: auto;
}

.section-with-label {
  display: flex;
  align-items: stretch;
}

.vertical-label {
  background: #1976d2;
  color: #fff;
  padding: 16px 8px;
  border-radius: 8px 0 0 8px;
  font-weight: bold;
  font-size: 16px;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  white-space: nowrap;
}

.profile-form {
  background: #e3f2fd;
  border-radius: 0 8px 8px 0;
  padding: 16px 24px;
  flex: 1;
}

/* 用户查询区域样式 */
.user-search-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #bbdefb;
}

.user-search-row > * {
  margin-right: 12px;
}

.user-search-row > *:last-child {
  margin-right: 0;
}

.user-search-row .search-input + .search-btn {
  margin-left: 0;
  margin-right: 6px;
}

.user-search-row .search-btn + .clear-btn {
  margin-left: 0;
  margin-right: 0;
}

.user-search-row label {
  font-weight: bold;
  color: #1976d2;
  white-space: nowrap;
}

.search-input {
  width: 180px;
  border: 1px solid #90caf9;
  border-radius: 4px;
  padding: 6px 12px;
  background: #fff;
  color: #333;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.search-btn, .clear-btn, .create-btn {
  padding: 0 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s;
}

.search-btn {
  background: #1976d2;
  color: white;
}

.search-btn:hover:not(:disabled) {
  background: #1565c0;
}

.search-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.clear-btn {
  background: #f44336;
  color: white;
}

.clear-btn:hover {
  background: #d32f2f;
}

.create-btn {
  background: #4caf50;
  color: white;
}

.create-btn:hover:not(:disabled) {
  background: #45a049;
}

.create-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 用户信息区域 */
.user-info-area {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 无用户提示 */
.no-user-tip {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
}

.no-user-tip p {
  margin: 0;
  font-size: 14px;
}

/* 用户下拉列表样式 */
.user-dropdown {
  position: relative;
  background: #fff;
  border: 1px solid #90caf9;
  border-radius: 8px;
  margin-top: 8px;
  box-shadow: 0 4px 12px rgba(33,150,243,0.15);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #e3f2fd;
  border-bottom: 1px solid #bbdefb;
  font-size: 12px;
  color: #1976d2;
  font-weight: bold;
}

.close-dropdown {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-dropdown:hover {
  color: #f44336;
}

.user-list {
  max-height: 250px;
  overflow-y: auto;
}

.user-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-item:hover {
  background: #f5f5f5;
}

.user-item:last-child {
  border-bottom: none;
}

.user-basic {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.user-name {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.user-phone {
  color: #1976d2;
  font-size: 13px;
  font-family: monospace;
}

.user-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.user-age, .user-gender, .user-agent {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}
.form-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}
.form-row label {
  font-weight: bold;
  color: #1976d2;
  margin-right: 4px;
}
.form-row input {
  border: 1px solid #90caf9;
  border-radius: 4px;
  padding: 2px 8px;
  background: #fff;
  color: #333;
  width: 100px;
}

/* 用户信息输入框样式 */
.name-input {
  width: 150px !important;
}

.age-input {
  width: 60px !important;
}

.gender-input {
  width: 50px !important;
}

.address-input {
  width: 350px !important;
}

/* 年龄性别行对齐样式 */
.age-gender-row {
  display: flex !important;
  gap: 0 !important;
  align-items: center;
  position: relative; /* 为绝对定位提供参考 */
}

.age-section {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: none;
}

.gender-section {
  display: flex;
  align-items: center;
  gap: 15px;
  position: absolute;
  left: 250px; /* 姓名标签(32px) + 姓名输入框(150px) + 间距(4px) = 186px */
}
.health-table {
  /* background: #e3f2fd; */
  border-radius: 8px;
  padding: 16px 24px;
  overflow-x: auto;
}
.health-table table {
  width: 100%;
  border-collapse: collapse;
  /* background: #fff; */
}
.health-table th, .health-table td {
  border: 1px solid #90caf9;
  padding: 6px 10px;
  text-align: center;
  font-size: 14px;
}
.health-table th {
  background: #1976d2;
  color: #fff;
}
.user-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  margin-left: 20px;
  border: 2px solid #fff;
  background: #eee;
  display: inline-block;
  vertical-align: middle;
}
.user-name {
  margin-left: 8px;
  color: #fff;
  font-size: 16px;
  vertical-align: middle;
  display: inline-block;
  font-weight: bold;
}
.user-info-wrapper {
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  margin-left: auto;
}
.user-card {
  position: absolute;
  top: 40px;
  right: 0;
  width: 260px;
  background: #fff;
  box-shadow: 0 4px 24px rgba(0,0,0,0.15);
  border-radius: 8px;
  padding: 16px;
  z-index: 100;
  color: #333;
}
.user-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.user-card-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 12px;
  border: 1px solid #eee;
}
.user-card-info {
  flex: 1;
}
.user-card-name {
  font-weight: bold;
  font-size: 18px;
  color: #1976d2;
}
.user-card-id {
  font-size: 13px;
  color: #888;
  margin-top: 2px;
}
.user-card-logout {
  width: 60%;
  background: #e57373;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 4px 0;
  font-size: 13px;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.user-card-logout:hover {
  background: #d32f2f;
}
.tab-content {
  background: #fff;
  border-radius: 0 8px 8px 0;
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(33,150,243,0.08);
}
</style> 
