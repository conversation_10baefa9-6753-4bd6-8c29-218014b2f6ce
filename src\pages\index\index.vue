<template>
  <view class="container" :class="backgroundTheme">
    <!-- 右上角按钮组 -->
    <view class="top-buttons">
      <view class="theme-btn" @click="switchBackground">切换背景</view>
      <view class="logout-btn" @click="handleLogout">退出</view>
    </view>
    <!-- 左侧导航栏 -->
    <view class="sidebar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: activeTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
    </view>
    
    <!-- 主内容区域 -->
    <view class="content">
      <!-- 数据录入 -->
      <view v-if="activeTab === 0" class="tab-content">
        <view class="panel">
          <view class="panel-header">
            <text class="panel-title">健康数据录入</text>
          </view>
          <view class="panel-body">
            <!-- 这里放数据录入表单 -->
            <text>数据录入功能开发中...</text>
          </view>
        </view>
      </view>
      
      <!-- 数据查询 -->
      <view v-if="activeTab === 1" class="tab-content">
        <view class="panel">
          <view class="panel-header">
            <text class="panel-title">健康数据查询</text>
          </view>
          <view class="panel-body">
            <!-- 这里放数据查询功能 -->
            <text>数据查询功能开发中...</text>
          </view>
        </view>
      </view>
      
      <!-- 数据分析 -->
      <view v-if="activeTab === 2" class="tab-content">
        <view class="panel">
          <view class="panel-header">
            <text class="panel-title">健康数据分析</text>
          </view>
          <view class="panel-body">
            <!-- 这里放数据分析功能 -->
            <text>数据分析功能开发中...</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { redirectTo, ROUTES } from '@/utils/router'
import { logout } from '@/api/user'
import { getItem, removeItem } from '@/utils/storage'
import store from '@/store'

export default {
  name: 'Index',
  data() {
    return {
      activeTab: 0,
      tabs: [
        { name: '数据录入', icon: '' },
        { name: '数据查询', icon: '' },
        { name: '数据分析', icon: '' }
      ],
      // 背景主题
      backgroundTheme: 'theme-image-login',
      backgroundThemes: [
        'theme-image-login',
        'theme-medical',
        'theme-tech',
        'theme-nature',
        'theme-warm',
        'theme-animated'
      ],
      currentThemeIndex: 0,
      // 用于保存各个tab的状态
      formData: {
        // 数据录入表单数据
      },
      queryParams: {
        // 数据查询参数
      },
      analysisParams: {
        // 数据分析参数
      }
    };
  },
  onLoad() {
    // 检查登录状态
    const token = uni.getStorageSync('token');
    if (!token) {
      redirectTo(ROUTES.LOGIN);
    }
  },
  methods: {
    switchTab(index) {
      this.activeTab = index;
    },
    switchBackground() {
      this.currentThemeIndex = (this.currentThemeIndex + 1) % this.backgroundThemes.length;
      this.backgroundTheme = this.backgroundThemes[this.currentThemeIndex];

      // 显示当前主题名称
      const themeNames = {
        'theme-image-login': '登录背景图',
        'theme-medical': '医疗渐变',
        'theme-tech': '科技蓝色',
        'theme-nature': '自然绿色',
        'theme-warm': '温暖橙色',
        'theme-animated': '动画背景'
      };

      uni.showToast({
        title: `已切换到: ${themeNames[this.backgroundTheme]}`,
        icon: 'none',
        duration: 1500
      });
    },
    async handleLogout() {
      try {
        const token = getItem('token');
        const userInfo = getItem('userInfo');
        const userId = userInfo && userInfo.id ? userInfo.id : '';
        if (!token || !userId) {
          // 本地无登录信息，直接清理并跳转
          store.commit('user/CLEAR_USER');
          removeItem('token');
          removeItem('userInfo');
          redirectTo(ROUTES.LOGIN);
          return;
        }
        await logout(token, userId);
      } catch (e) {
        // 可根据需要提示错误
        console.error('退出失败', e);
      } finally {
        store.commit('user/CLEAR_USER');
        removeItem('token');
        removeItem('userInfo');
        redirectTo(ROUTES.LOGIN);
      }
    }
  }
}
</script>

<style>
.container {
  display: flex;
  height: 100vh;
  width: 100%;
  /* 方案一：使用现有的登录背景图 */
  background-image: url('/static/images/index-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;

  /* 添加半透明遮罩层以提高内容可读性 */
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(2px);
  z-index: 1;
}

/* 确保内容在遮罩层之上 */
.sidebar,
.content,
.logout-btn {
  position: relative;
  z-index: 2;
}

.sidebar {
  width: 200rpx;
  background-color: rgba(0, 150, 136, 0.9);
  height: 100%;
  box-shadow: 2rpx 0 10rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.tab-item {
  padding: 30rpx 20rpx;
  text-align: center;
  color: #e0f2f1;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #00796b;
  color: white;
  font-weight: bold;
  border-left: 8rpx solid #b2dfdb;
}

.tab-text {
  font-size: 28rpx;
}

.content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.tab-content {
  height: 100%;
}

.panel {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.panel-header {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  background-color: #f9f9f9;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #009688;
}

.panel-body {
  padding: 30rpx;
}

.top-buttons {
  position: absolute;
  top: 20rpx;
  right: 40rpx;
  z-index: 10;
  display: flex;
  gap: 20rpx;
}

.theme-btn {
  background: #4caf50;
  color: #fff;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transition: background 0.2s;
}

.theme-btn:hover {
  background: #388e3c;
}

.logout-btn {
  background: #e57373;
  color: #fff;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transition: background 0.2s;
}

.logout-btn:hover {
  background: #d32f2f;
}

/* 背景主题样式 */
.theme-medical {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
}

.theme-tech {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.theme-nature {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.theme-warm {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
}

.theme-image-login {
  background-image: url('/static/images/login-bg.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-attachment: fixed !important;
}

@keyframes backgroundShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.theme-animated {
  background: linear-gradient(-45deg, #667eea, #764ba2, #a8edea, #fed6e3) !important;
  background-size: 400% 400% !important;
  animation: backgroundShift 15s ease infinite !important;
}
</style> 
