<template>
  <view class="container">
    <!-- 右上角退出按钮 -->
    <view class="logout-btn" @click="handleLogout">退出</view>
    <!-- 左侧导航栏 -->
    <view class="sidebar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: activeTab === index }"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
      </view>
    </view>
    
    <!-- 主内容区域 -->
    <view class="content">
      <!-- 数据录入 -->
      <view v-if="activeTab === 0" class="tab-content">
        <view class="panel">
          <view class="panel-header">
            <text class="panel-title">健康数据录入</text>
          </view>
          <view class="panel-body">
            <!-- 这里放数据录入表单 -->
            <text>数据录入功能开发中...</text>
          </view>
        </view>
      </view>
      
      <!-- 数据查询 -->
      <view v-if="activeTab === 1" class="tab-content">
        <view class="panel">
          <view class="panel-header">
            <text class="panel-title">健康数据查询</text>
          </view>
          <view class="panel-body">
            <!-- 这里放数据查询功能 -->
            <text>数据查询功能开发中...</text>
          </view>
        </view>
      </view>
      
      <!-- 数据分析 -->
      <view v-if="activeTab === 2" class="tab-content">
        <view class="panel">
          <view class="panel-header">
            <text class="panel-title">健康数据分析</text>
          </view>
          <view class="panel-body">
            <!-- 这里放数据分析功能 -->
            <text>数据分析功能开发中...</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { redirectTo, ROUTES } from '@/utils/router'
import { logout } from '@/api/user'
import { getItem, removeItem } from '@/utils/storage'
import store from '@/store'

export default {
  name: 'Index',
  data() {
    return {
      activeTab: 0,
      tabs: [
        { name: '数据录入', icon: '' },
        { name: '数据查询', icon: '' },
        { name: '数据分析', icon: '' }
      ],
      // 用于保存各个tab的状态
      formData: {
        // 数据录入表单数据
      },
      queryParams: {
        // 数据查询参数
      },
      analysisParams: {
        // 数据分析参数
      }
    };
  },
  onLoad() {
    // 检查登录状态
    const token = uni.getStorageSync('token');
    if (!token) {
      redirectTo(ROUTES.LOGIN);
    }
  },
  methods: {
    switchTab(index) {
      this.activeTab = index;
    },
    async handleLogout() {
      try {
        const token = getItem('token');
        const userInfo = getItem('userInfo');
        const userId = userInfo && userInfo.id ? userInfo.id : '';
        if (!token || !userId) {
          // 本地无登录信息，直接清理并跳转
          store.commit('user/CLEAR_USER');
          removeItem('token');
          removeItem('userInfo');
          redirectTo(ROUTES.LOGIN);
          return;
        }
        await logout(token, userId);
      } catch (e) {
        // 可根据需要提示错误
        console.error('退出失败', e);
      } finally {
        store.commit('user/CLEAR_USER');
        removeItem('token');
        removeItem('userInfo');
        redirectTo(ROUTES.LOGIN);
      }
    }
  }
}
</script>

<style>
.container {
  background-image: url('/static/images/index-bg.jpg');
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
}

.sidebar {
  width: 200rpx;
  background-color: #009688;
  height: 100%;
  box-shadow: 2rpx 0 10rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  padding: 30rpx 20rpx;
  text-align: center;
  color: #e0f2f1;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #00796b;
  color: white;
  font-weight: bold;
  border-left: 8rpx solid #b2dfdb;
}

.tab-text {
  font-size: 28rpx;
}

.content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.tab-content {
  height: 100%;
}

.panel {
  background-color: white;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.panel-header {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  background-color: #f9f9f9;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #009688;
}

.panel-body {
  padding: 30rpx;
}

.logout-btn {
  position: absolute;
  top: 20rpx;
  right: 40rpx;
  z-index: 10;
  background: #e57373;
  color: #fff;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transition: background 0.2s;
}
.logout-btn:hover {
  background: #d32f2f;
}
</style> 
