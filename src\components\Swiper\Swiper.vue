<template>
  <view class="swiper-container">
    <swiper 
      class="swiper" 
      :indicator-dots="true" 
      :autoplay="true" 
      :interval="3000" 
      :duration="500"
      indicator-active-color="#3cc51f"
      indicator-color="rgba(255, 255, 255, 0.5)"
    >
      <swiper-item v-for="(item, index) in items" :key="index">
        <image :src="item.image" mode="aspectFill" class="swiper-image"></image>
        <view class="swiper-title">{{ item.title }}</view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup>
defineProps({
  items: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
.swiper-container {
  width: 100%;
  height: 400rpx;
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.swiper-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 20rpx;
  font-size: 28rpx;
}
</style> 