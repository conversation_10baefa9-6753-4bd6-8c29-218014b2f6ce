/* 重置样式 */
page, view, text, image, swiper, swiper-item, navigator {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

/* 通用样式 */
.container {
  padding: 20rpx;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }

.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }

.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.ml-30 { margin-left: 30rpx; }

.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }
.mr-30 { margin-right: 30rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 文本样式 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #3cc51f;
}

.text-danger {
  color: #ff5a5f;
}

.text-warning {
  color: #ffb800;
}

.text-info {
  color: #2d8cf0;
}

.text-gray {
  color: #999;
}

.text-bold {
  font-weight: bold;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 按钮样式 */
.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.btn-primary {
  background-color: #3cc51f;
  color: #fff;
}

.btn-outline {
  border: 1rpx solid #3cc51f;
  color: #3cc51f;
  background-color: transparent;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #eee;
  margin: 20rpx 0;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.tag-primary {
  background-color: rgba(60, 197, 31, 0.1);
  color: #3cc51f;
}

.tag-danger {
  background-color: rgba(255, 90, 95, 0.1);
  color: #ff5a5f;
} 