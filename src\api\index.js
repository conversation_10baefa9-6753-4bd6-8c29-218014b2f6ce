
// Mock数据，实际项目中应该从服务器获取
const mockHomeData = {
  banners: [
    { id: 1, image: '/static/images/banner1.jpg', title: '伊代欣糖生产基地' },
    { id: 2, image: '/static/images/banner2.jpg', title: '专业研发团队' },
    { id: 3, image: '/static/images/banner3.jpg', title: '高品质产品' }
  ],
  featuredProducts: [
    { id: 1, name: '伊代欣糖标准品', shortDesc: '99%纯度标准品', image: '/static/images/product1.jpg' },
    { id: 2, name: '伊代欣糖食品级', shortDesc: '食品级应用', image: '/static/images/product2.jpg' },
    { id: 3, name: '伊代欣糖医药级', shortDesc: '医药级应用', image: '/static/images/product3.jpg' }
  ],
  advantages: [
    { title: '专业研发', desc: '拥有专业的研发团队和先进的实验设备', icon: '/static/images/icon-research.png' },
    { title: '品质保障', desc: '严格的质量管理体系，确保产品品质', icon: '/static/images/icon-quality.png' },
    { title: '服务完善', desc: '提供全方位的技术支持和售后服务', icon: '/static/images/icon-service.png' }
  ]
}

const mockProductData = {
  categories: [
    { id: 'all', name: '全部' },
    { id: 'standard', name: '标准品' },
    { id: 'food', name: '食品级' },
    { id: 'medical', name: '医药级' }
  ],
  products: [
    { 
      id: 1, 
      name: '伊代欣糖标准品', 
      shortDesc: '99%纯度标准品', 
      specification: '25g/瓶',
      categoryId: 'standard',
      image: '/static/images/product1.jpg',
      isNew: true,
      isHot: false
    },
    { 
      id: 2, 
      name: '伊代欣糖食品级', 
      shortDesc: '食品级应用', 
      specification: '1kg/袋',
      categoryId: 'food',
      image: '/static/images/product2.jpg',
      isNew: false,
      isHot: true
    },
    { 
      id: 3, 
      name: '伊代欣糖医药级', 
      shortDesc: '医药级应用', 
      specification: '500g/瓶',
      categoryId: 'medical',
      image: '/static/images/product3.jpg',
      isNew: false,
      isHot: false
    },
    { 
      id: 4, 
      name: '伊代欣糖超纯品', 
      shortDesc: '99.9%超高纯度', 
      specification: '10g/瓶',
      categoryId: 'standard',
      image: '/static/images/product4.jpg',
      isNew: true,
      isHot: true
    }
  ]
}

const mockAboutData = {
  history: [
    { year: '2010年', event: '北京宓元堂生物科技有限公司成立' },
    { year: '2012年', event: '建立伊代欣糖研发中心，开始研发伊代欣糖产品' },
    { year: '2015年', event: '获得ISO9001质量管理体系认证' },
    { year: '2017年', event: '获得ISO22000食品安全管理体系认证' },
    { year: '2019年', event: '伊代欣糖产品线扩展，推出医药级和食品级系列产品' },
    { year: '2021年', event: '被评为"北京市高新技术企业"' },
    { year: '2023年', event: '产品出口到欧美等20多个国家和地区' }
  ]
}

// 首页相关接口
export const homeApi = {
  // 获取首页数据
  getHomeData() {
    // 实际项目中应该调用request
    // return request({
    //   url: '/home/<USER>',
    //   method: 'GET'
    // })
    
    // 使用Mock数据
    return Promise.resolve(mockHomeData)
  }
}

// 产品相关接口
export const productApi = {
  // 获取产品列表
  getProductList(params) {
    // 实际项目中应该调用request
    // return request({
    //   url: '/products',
    //   method: 'GET',
    //   data: params
    // })
    
    // 使用Mock数据
    return Promise.resolve(mockProductData)
  },
  
  // 获取产品详情
  getProductDetail(id) {
    // 实际项目中应该调用request
    // return request({
    //   url: `/products/${id}`,
    //   method: 'GET'
    // })
    
    // 使用Mock数据
    const product = mockProductData.products.find(item => item.id === Number(id))
    return Promise.resolve(product || null)
  }
}

// 关于我们相关接口
export const aboutApi = {
  // 获取关于我们数据
  getAboutData() {
    // 实际项目中应该调用request
    // return request({
    //   url: '/about',
    //   method: 'GET'
    // })
    
    // 使用Mock数据
    return Promise.resolve(mockAboutData)
  }
} 