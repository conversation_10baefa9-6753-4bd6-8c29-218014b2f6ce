import { API_BASE_URL } from '../config/env';

export default function request({ url, method = 'GET', data = {}, header = {} }) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: API_BASE_URL + url,
      method,
      data,
      header,
      timeout: 10000,
      success: res => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          uni.showToast({ title: '网络错误', icon: 'none' });
          reject(res);
        }
      },
      fail: err => {
        uni.showToast({ title: '请求失败', icon: 'none' });
        reject(err);
      }
    });
  });
} 