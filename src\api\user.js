import request from './request';
import { getItem } from '@/utils/storage';

export function login({ phoneNumber, password }) {
  return request({
    url: '/api/users/login',
    method: 'POST',
    data: { phoneNumber, password }
  });
}

export function logout(token, userId) {
  return request({
    url: `/api/users/logout?userId=${userId}`,
    method: 'POST',
    header: {
      Authorization: `Bearer ${token}`
    }
  });
}

/**
 * 根据代理商ID和手机号查询用户列表
 * @param {Object} params 查询参数
 * @param {number} params.agentId 代理商ID
 * @param {string} params.phoneNumber 手机号
 * @param {number} params.currentUserId 当前用户ID
 * @param {string} params.currentUserType 当前用户类型
 * @returns {Promise} API响应
 */
export function getUsersByAgent(params) {
  const { agentId, phoneNumber, currentUserId, currentUserType } = params;

  const queryParams = new URLSearchParams();

  if (agentId) {
    queryParams.append('agentId', agentId);
  }

  if (phoneNumber) {
    queryParams.append('phoneNumber', phoneNumber);
  }

  const url = `/api/users?${queryParams.toString()}`;

  const headers = {
    'Authorization': `Bearer ${getItem('token')}`,
    'Content-Type': 'application/json'
  };

  if (currentUserId) {
    headers['currentUserId'] = currentUserId;
  }

  if (currentUserType) {
    headers['currentUserType'] = currentUserType;
  }

  return request({
    url,
    method: 'GET',
    header: headers
  });
}

/**
 * 根据手机号精确查询单个用户
 * @param {string} phoneNumber 手机号
 * @returns {Promise} API响应
 */
export function getUserByPhone(phoneNumber) {
  const userInfo = getItem('userInfo');
  const agentId = userInfo?.id;
  const currentUserType = userInfo?.userType || 'AGENT';

  return getUsersByAgent({
    agentId,
    phoneNumber,
    currentUserId: agentId,
    currentUserType
  });
}

/**
 * 用户注册接口
 * @param {Object} userData 用户注册数据
 * @param {string} userData.phoneNumber 手机号
 * @param {string} userData.password 密码
 * @param {string} userData.realName 真实姓名
 * @param {string} userData.gender 性别
 * @param {number} userData.age 年龄
 * @param {string} userData.address 地址
 * @returns {Promise} API响应
 */
export function registerUser(userData) {
  const userInfo = getItem('userInfo');
  const currentUserId = userInfo?.id;
  const currentUserType = userInfo?.userType || 'AGENT';

  const headers = {
    'Authorization': `Bearer ${getItem('token')}`,
    'Content-Type': 'application/json'
  };

  if (currentUserId) {
    headers['currentUserId'] = currentUserId;
  }

  if (currentUserType) {
    headers['currentUserType'] = currentUserType;
  }

  return request({
    url: '/api/users/register',
    method: 'POST',
    data: userData,
    header: headers
  });
}