/* 背景主题样式配置文件 */

/* 主题1: 医疗健康渐变背景 */
.theme-medical {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* 主题2: 科技蓝色渐变 */
.theme-tech {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 主题3: 自然绿色渐变 */
.theme-nature {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 主题4: 温暖橙色渐变 */
.theme-warm {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 主题5: 图片背景 - 登录背景 */
.theme-image-login {
  background-image: url('/static/images/login-bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* 主题6: 图片背景 - 微信图片 */
.theme-image-wechat {
  background-image: url('/static/images/微信图片_20250616145714.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* 主题7: 几何图案背景 */
.theme-geometric {
  background-color: #667eea;
  background-image: 
    radial-gradient(circle at 25% 25%, #ffffff 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, #ffffff 2px, transparent 2px);
  background-size: 50px 50px;
}

/* 主题8: 波浪背景 */
.theme-wave {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
}

/* 通用遮罩层样式 */
.background-overlay {
  position: relative;
}

.background-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(2px);
  z-index: 1;
}

/* 毛玻璃效果组件 */
.glass-effect {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.glass-effect-dark {
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  color: white;
}

/* 侧边栏毛玻璃效果 */
.sidebar-glass {
  background-color: rgba(0, 150, 136, 0.9);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

/* 动画背景 */
@keyframes backgroundShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.theme-animated {
  background: linear-gradient(-45deg, #667eea, #764ba2, #a8edea, #fed6e3);
  background-size: 400% 400%;
  animation: backgroundShift 15s ease infinite;
}
