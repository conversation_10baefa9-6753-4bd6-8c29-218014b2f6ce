<template>
  <div class="health-assessment-panel">
    <div class="panel-header">
      <h3>健康评估</h3>
      <p class="panel-desc">基于您的健康数据进行综合评估分析</p>
    </div>
    
    <!-- 评估类型选择 -->
    <div class="assessment-types">
      <div 
        v-for="(type, index) in assessmentTypes" 
        :key="index"
        class="type-card"
        :class="{ active: activeType === index }"
        @click="selectType(index)"
      >
        <div class="type-icon">{{ type.icon }}</div>
        <div class="type-name">{{ type.name }}</div>
        <div class="type-desc">{{ type.desc }}</div>
      </div>
    </div>

    <!-- 评估表单 -->
    <div class="assessment-form" v-if="activeType !== -1">
      <h4>{{ assessmentTypes[activeType].name }}评估</h4>
      
      <div class="form-section">
        <div class="form-group" v-for="(question, qIndex) in currentQuestions" :key="qIndex">
          <label class="question-label">{{ question.text }}</label>
          <div class="answer-options">
            <label 
              v-for="(option, oIndex) in question.options" 
              :key="oIndex"
              class="option-label"
            >
              <input 
                type="radio" 
                :name="`question_${qIndex}`"
                :value="option.value"
                v-model="answers[qIndex]"
              />
              <span>{{ option.text }}</span>
            </label>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button class="btn-primary" @click="submitAssessment" :disabled="!isFormComplete">
          开始评估
        </button>
        <button class="btn-secondary" @click="resetForm">重置</button>
      </div>
    </div>

    <!-- 评估结果 -->
    <div class="assessment-result" v-if="showResult">
      <h4>评估结果</h4>
      <div class="result-card">
        <div class="score-section">
          <div class="score-circle">
            <span class="score">{{ assessmentScore }}</span>
            <span class="score-label">分</span>
          </div>
          <div class="score-desc">
            <h5>{{ getScoreLevel() }}</h5>
            <p>{{ getScoreDescription() }}</p>
          </div>
        </div>
        
        <div class="recommendations">
          <h5>建议措施</h5>
          <ul>
            <li v-for="(rec, index) in recommendations" :key="index">
              {{ rec }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HealthAssessment',
  data() {
    return {
      activeType: -1,
      answers: {},
      showResult: false,
      assessmentScore: 0,
      assessmentTypes: [
        {
          name: '心血管健康',
          icon: '❤️',
          desc: '评估心脏和血管系统健康状况'
        },
        {
          name: '营养状况',
          icon: '🥗',
          desc: '分析营养摄入和身体营养状态'
        },
        {
          name: '运动能力',
          icon: '🏃',
          desc: '评估身体运动能力和体能水平'
        },
        {
          name: '心理健康',
          icon: '🧠',
          desc: '评估心理状态和精神健康水平'
        }
      ],
      questions: {
        0: [ // 心血管健康
          {
            text: '您是否经常感到胸闷或心悸？',
            options: [
              { text: '从不', value: 4 },
              { text: '偶尔', value: 3 },
              { text: '经常', value: 2 },
              { text: '总是', value: 1 }
            ]
          },
          {
            text: '您的血压情况如何？',
            options: [
              { text: '正常', value: 4 },
              { text: '偏高', value: 2 },
              { text: '偏低', value: 2 },
              { text: '不清楚', value: 1 }
            ]
          }
        ],
        1: [ // 营养状况
          {
            text: '您每天的饮食是否均衡？',
            options: [
              { text: '非常均衡', value: 4 },
              { text: '比较均衡', value: 3 },
              { text: '一般', value: 2 },
              { text: '不均衡', value: 1 }
            ]
          }
        ],
        2: [ // 运动能力
          {
            text: '您每周运动几次？',
            options: [
              { text: '5次以上', value: 4 },
              { text: '3-4次', value: 3 },
              { text: '1-2次', value: 2 },
              { text: '几乎不运动', value: 1 }
            ]
          }
        ],
        3: [ // 心理健康
          {
            text: '您最近的睡眠质量如何？',
            options: [
              { text: '很好', value: 4 },
              { text: '较好', value: 3 },
              { text: '一般', value: 2 },
              { text: '很差', value: 1 }
            ]
          }
        ]
      }
    }
  },
  computed: {
    currentQuestions() {
      return this.activeType !== -1 ? this.questions[this.activeType] : []
    },
    isFormComplete() {
      const questionCount = this.currentQuestions.length
      const answerCount = Object.keys(this.answers).length
      return questionCount > 0 && answerCount === questionCount
    },
    recommendations() {
      const score = this.assessmentScore
      if (score >= 80) {
        return ['保持当前良好的健康状态', '定期进行健康检查', '继续健康的生活方式']
      } else if (score >= 60) {
        return ['适当调整生活方式', '增加运动量', '注意饮食均衡', '定期监测健康指标']
      } else {
        return ['建议咨询专业医生', '制定详细的健康改善计划', '密切监测健康状况', '考虑专业治疗']
      }
    }
  },
  methods: {
    selectType(index) {
      this.activeType = index
      this.resetForm()
    },
    resetForm() {
      this.answers = {}
      this.showResult = false
      this.assessmentScore = 0
    },
    submitAssessment() {
      // 计算评估分数
      let totalScore = 0
      let maxScore = 0
      
      this.currentQuestions.forEach((question, index) => {
        const answer = this.answers[index]
        if (answer) {
          totalScore += parseInt(answer)
        }
        maxScore += 4 // 最高分为4
      })
      
      this.assessmentScore = Math.round((totalScore / maxScore) * 100)
      this.showResult = true
    },
    getScoreLevel() {
      const score = this.assessmentScore
      if (score >= 80) return '优秀'
      if (score >= 60) return '良好'
      if (score >= 40) return '一般'
      return '需要改善'
    },
    getScoreDescription() {
      const score = this.assessmentScore
      if (score >= 80) return '您的健康状况非常好，请继续保持！'
      if (score >= 60) return '您的健康状况良好，但还有改善空间。'
      if (score >= 40) return '您的健康状况一般，建议采取措施改善。'
      return '您的健康状况需要重点关注，建议咨询专业医生。'
    }
  }
}
</script>

<style scoped>
.health-assessment-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(33,150,243,0.08);
}

.panel-header {
  margin-bottom: 24px;
  text-align: center;
}

.panel-header h3 {
  color: #1976d2;
  margin-bottom: 8px;
}

.panel-desc {
  color: #666;
  font-size: 14px;
}

.assessment-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.type-card {
  border: 2px solid #e3f2fd;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.type-card:hover {
  border-color: #2196f3;
  box-shadow: 0 4px 12px rgba(33,150,243,0.15);
}

.type-card.active {
  border-color: #1976d2;
  background: #e3f2fd;
}

.type-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.type-name {
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 4px;
}

.type-desc {
  font-size: 12px;
  color: #666;
}

.assessment-form {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.question-label {
  display: block;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.answer-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.option-label:hover {
  background: #e3f2fd;
}

.option-label input {
  margin-right: 8px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.btn-primary, .btn-secondary {
  padding: 10px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
}

.btn-primary {
  background: #2196f3;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1976d2;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
}

.btn-secondary:hover {
  background: #e0e0e0;
}

.assessment-result {
  background: #e8f5e8;
  border-radius: 8px;
  padding: 20px;
}

.result-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.score-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #4caf50;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 20px;
}

.score {
  font-size: 24px;
  font-weight: bold;
}

.score-label {
  font-size: 12px;
}

.score-desc h5 {
  color: #2e7d32;
  margin-bottom: 8px;
}

.recommendations h5 {
  color: #1976d2;
  margin-bottom: 12px;
}

.recommendations ul {
  list-style: none;
  padding: 0;
}

.recommendations li {
  padding: 4px 0;
  padding-left: 16px;
  position: relative;
}

.recommendations li::before {
  content: '•';
  color: #4caf50;
  position: absolute;
  left: 0;
}
</style>
