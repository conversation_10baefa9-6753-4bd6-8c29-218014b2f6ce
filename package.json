{"name": "fuyuantang-web", "version": "1.0.0", "private": true, "scripts": {"dev": "npx uni -p h5 --mode development", "test": "npx uni -p h5 --mode test", "dev:h5": "npx uni -p h5", "dev:mp-weixin": "npx uni -p mp-weixin", "build:h5": "npx uni build", "build:mp-weixin": "npx uni build -p mp-weixin", "h5": "npx uni build", "build:test": "cross-env NODE_ENV=test uni build --mode test", "build:prod": "cross-env NODE_ENV=production uni build --mode production"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-alpha-3080720230703001", "@dcloudio/uni-h5": "^3.0.0-alpha-3080720230703001", "@dcloudio/uni-mp-weixin": "^3.0.0-alpha-3080720230703001", "axios": "^1.9.0", "vue": "^3.2.45", "vuex": "^4.1.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0-alpha-3080720230703001", "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-3080720230703001", "@vue/compiler-sfc": "^3.2.45", "cross-env": "^7.0.3", "vite": "^4.0.0"}}