const state = {
  token: uni.getStorageSync('token') || '',
  userInfo: uni.getStorageSync('userInfo') ? JSON.parse(uni.getStorageSync('userInfo')) : null
}
console.log('[Vuex:user] state 初始化:', state)

const mutations = {
  SET_TOKEN(state, token) {
    console.log('[Vuex:user] SET_TOKEN', token)
    state.token = token
    uni.setStorageSync('token', token)
  },
  SET_USER_INFO(state, userInfo) {
    console.log('[Vuex:user] SET_USER_INFO', userInfo)
    state.userInfo = userInfo
    uni.setStorageSync('userInfo', JSON.stringify(userInfo))
  },
  CLEAR_USER(state) {
    console.log('[Vuex:user] CLEAR_USER')
    state.token = ''
    state.userInfo = null
    uni.removeStorageSync('token')
    uni.removeStorageSync('userInfo')
  }
}

const actions = {
  // 登录
  login({ commit }, userInfo) {
    console.log('[Vuex:user] actions.login 调用', userInfo)
    // 这里是模拟登录，实际项目中应该调用接口
    return new Promise((resolve) => {
      setTimeout(() => {
        const token = 'mock-token-' + Date.now()
        commit('SET_TOKEN', token)
        commit('SET_USER_INFO', userInfo)
        resolve({ token, userInfo })
      }, 1000)
    })
  },
  
  // 登出
  logout({ commit }) {
    console.log('[Vuex:user] actions.logout 调用')
    commit('CLEAR_USER')
    return Promise.resolve()
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
} 