import { productApi } from '@/api/index'

const state = {
  productList: [],
  categories: [],
  currentProduct: null,
  loading: false
}

const mutations = {
  SET_PRODUCT_LIST(state, products) {
    state.productList = products
  },
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  },
  SET_CURRENT_PRODUCT(state, product) {
    state.currentProduct = product
  },
  SET_LOADING(state, status) {
    state.loading = status
  }
}

const actions = {
  // 获取产品列表
  async getProductList({ commit }, params) {
    commit('SET_LOADING', true)
    try {
      const result = await productApi.getProductList(params)
      commit('SET_PRODUCT_LIST', result.products)
      commit('SET_CATEGORIES', result.categories)
      commit('SET_LOADING', false)
      return result
    } catch (error) {
      commit('SET_LOADING', false)
      throw error
    }
  },
  
  // 获取产品详情
  async getProductDetail({ commit }, id) {
    commit('SET_LOADING', true)
    try {
      const product = await productApi.getProductDetail(id)
      commit('SET_CURRENT_PRODUCT', product)
      commit('SET_LOADING', false)
      return product
    } catch (error) {
      commit('SET_LOADING', false)
      throw error
    }
  }
}

const getters = {
  // 根据分类获取产品
  productsByCategory: (state) => (categoryId) => {
    if (!categoryId || categoryId === 'all') {
      return state.productList
    }
    return state.productList.filter(item => item.categoryId === categoryId)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 