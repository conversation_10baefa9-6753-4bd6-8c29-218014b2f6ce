<template>
  <div class="rehab-center-panel">
    <div class="panel-header">
      <h3>康复中心</h3>
      <p class="panel-desc">专业康复训练与治疗服务</p>
    </div>

    <!-- 康复服务分类 -->
    <div class="service-categories">
      <div 
        v-for="(category, index) in serviceCategories" 
        :key="index"
        class="service-card"
        @click="selectService(index)"
      >
        <div class="service-icon">{{ category.icon }}</div>
        <h4 class="service-title">{{ category.title }}</h4>
        <p class="service-desc">{{ category.description }}</p>
        <div class="service-stats">
          <span class="stat">{{ category.sessions }}次疗程</span>
          <span class="stat">{{ category.duration }}</span>
        </div>
      </div>
    </div>

    <!-- 康复计划 -->
    <div class="rehab-plan-section">
      <h4>个人康复计划</h4>
      
      <div class="current-plan" v-if="currentPlan">
        <div class="plan-header">
          <div class="plan-info">
            <h5>{{ currentPlan.name }}</h5>
            <p class="plan-type">{{ currentPlan.type }}</p>
          </div>
          <div class="plan-progress">
            <div class="progress-circle">
              <span class="progress-text">{{ currentPlan.progress }}%</span>
            </div>
          </div>
        </div>
        
        <div class="plan-timeline">
          <div 
            v-for="(phase, pIndex) in currentPlan.phases" 
            :key="pIndex"
            class="timeline-item"
            :class="{ 
              completed: phase.status === 'completed',
              current: phase.status === 'current',
              upcoming: phase.status === 'upcoming'
            }"
          >
            <div class="timeline-marker"></div>
            <div class="timeline-content">
              <h6>{{ phase.title }}</h6>
              <p>{{ phase.description }}</p>
              <div class="phase-details">
                <span class="phase-duration">{{ phase.duration }}</span>
                <span class="phase-exercises">{{ phase.exercises.length }}项训练</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="no-plan" v-else>
        <div class="no-plan-content">
          <div class="no-plan-icon">🏥</div>
          <h5>暂无康复计划</h5>
          <p>请联系康复师制定个性化康复方案</p>
          <button class="btn-primary" @click="requestPlan">申请康复计划</button>
        </div>
      </div>
    </div>

    <!-- 今日训练 -->
    <div class="today-training" v-if="todayExercises.length > 0">
      <h4>今日康复训练</h4>
      
      <div class="exercise-list">
        <div 
          v-for="(exercise, eIndex) in todayExercises" 
          :key="eIndex"
          class="exercise-item"
          :class="{ completed: exercise.completed }"
        >
          <div class="exercise-info">
            <div class="exercise-icon">{{ exercise.icon }}</div>
            <div class="exercise-details">
              <h6>{{ exercise.name }}</h6>
              <p class="exercise-desc">{{ exercise.description }}</p>
              <div class="exercise-params">
                <span v-if="exercise.sets">{{ exercise.sets }}组</span>
                <span v-if="exercise.reps">{{ exercise.reps }}次/组</span>
                <span v-if="exercise.duration">{{ exercise.duration }}</span>
              </div>
            </div>
          </div>
          
          <div class="exercise-actions">
            <button 
              class="btn-complete"
              :class="{ completed: exercise.completed }"
              @click="toggleExerciseComplete(eIndex)"
            >
              {{ exercise.completed ? '已完成' : '标记完成' }}
            </button>
            <button class="btn-video" @click="watchVideo(exercise)">
              📹 视频
            </button>
          </div>
        </div>
      </div>
      
      <div class="training-summary">
        <div class="summary-stats">
          <div class="stat-item">
            <span class="stat-value">{{ completedExercises }}</span>
            <span class="stat-label">已完成</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ todayExercises.length }}</span>
            <span class="stat-label">总训练</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ completionRate }}%</span>
            <span class="stat-label">完成率</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 康复记录 -->
    <div class="rehab-records">
      <h4>康复记录</h4>
      
      <div class="record-filters">
        <select v-model="recordFilter" class="filter-select">
          <option value="all">全部记录</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
        </select>
      </div>
      
      <div class="records-list">
        <div 
          v-for="(record, rIndex) in filteredRecords" 
          :key="rIndex"
          class="record-item"
        >
          <div class="record-date">
            <span class="date">{{ record.date }}</span>
            <span class="time">{{ record.time }}</span>
          </div>
          <div class="record-content">
            <h6>{{ record.title }}</h6>
            <p>{{ record.description }}</p>
            <div class="record-metrics" v-if="record.metrics">
              <span 
                v-for="(metric, mIndex) in record.metrics" 
                :key="mIndex"
                class="metric"
              >
                {{ metric.name }}: {{ metric.value }}
              </span>
            </div>
          </div>
          <div class="record-status">
            <span class="status-badge" :class="record.status">
              {{ getStatusText(record.status) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约康复师 -->
    <div class="appointment-section">
      <h4>预约康复师</h4>
      
      <div class="therapist-list">
        <div 
          v-for="(therapist, tIndex) in therapists" 
          :key="tIndex"
          class="therapist-card"
        >
          <div class="therapist-avatar">
            <img :src="therapist.avatar" :alt="therapist.name" />
          </div>
          <div class="therapist-info">
            <h6>{{ therapist.name }}</h6>
            <p class="therapist-title">{{ therapist.title }}</p>
            <p class="therapist-specialty">{{ therapist.specialty }}</p>
            <div class="therapist-rating">
              <span class="rating">⭐ {{ therapist.rating }}</span>
              <span class="experience">{{ therapist.experience }}年经验</span>
            </div>
          </div>
          <div class="therapist-actions">
            <button class="btn-appointment" @click="bookAppointment(therapist)">
              预约
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RehabCenter',
  data() {
    return {
      recordFilter: 'all',
      serviceCategories: [
        {
          icon: '🦴',
          title: '骨科康复',
          description: '骨折、关节置换术后康复',
          sessions: 12,
          duration: '6-8周'
        },
        {
          icon: '🧠',
          title: '神经康复',
          description: '中风、脊髓损伤康复',
          sessions: 20,
          duration: '3-6个月'
        },
        {
          icon: '❤️',
          title: '心肺康复',
          description: '心脏病、呼吸系统康复',
          sessions: 16,
          duration: '8-12周'
        },
        {
          icon: '🏃',
          title: '运动康复',
          description: '运动损伤、体能恢复',
          sessions: 10,
          duration: '4-6周'
        }
      ],
      currentPlan: {
        name: '膝关节置换术后康复',
        type: '骨科康复',
        progress: 65,
        phases: [
          {
            title: '急性期康复',
            description: '消肿止痛，维持关节活动度',
            duration: '术后1-2周',
            status: 'completed',
            exercises: ['被动活动', '肌肉收缩']
          },
          {
            title: '功能恢复期',
            description: '增强肌力，改善关节功能',
            duration: '术后3-6周',
            status: 'current',
            exercises: ['主动训练', '负重练习', '平衡训练']
          },
          {
            title: '强化训练期',
            description: '全面恢复日常活动能力',
            duration: '术后7-12周',
            status: 'upcoming',
            exercises: ['功能性训练', '耐力训练']
          }
        ]
      },
      todayExercises: [
        {
          name: '膝关节屈伸训练',
          description: '坐位或仰卧位进行膝关节屈伸运动',
          icon: '🦵',
          sets: 3,
          reps: 15,
          completed: false
        },
        {
          name: '股四头肌训练',
          description: '加强大腿前侧肌肉力量',
          icon: '💪',
          sets: 3,
          reps: 12,
          completed: true
        },
        {
          name: '平衡训练',
          description: '单腿站立平衡练习',
          icon: '⚖️',
          duration: '5分钟',
          completed: false
        }
      ],
      rehabRecords: [
        {
          date: '2024-01-15',
          time: '14:30',
          title: '康复训练',
          description: '完成膝关节活动度训练和肌力训练',
          status: 'completed',
          metrics: [
            { name: '关节活动度', value: '120°' },
            { name: '疼痛评分', value: '3/10' }
          ]
        },
        {
          date: '2024-01-14',
          time: '10:00',
          title: '康复评估',
          description: '康复师评估训练效果',
          status: 'assessed',
          metrics: [
            { name: '肌力等级', value: '4级' },
            { name: '步行距离', value: '200米' }
          ]
        }
      ],
      therapists: [
        {
          name: '张康复师',
          title: '主任康复师',
          specialty: '骨科康复、运动康复',
          rating: 4.9,
          experience: 15,
          avatar: '/static/images/therapist1.jpg'
        },
        {
          name: '李康复师',
          title: '康复师',
          specialty: '神经康复、心肺康复',
          rating: 4.8,
          experience: 8,
          avatar: '/static/images/therapist2.jpg'
        }
      ]
    }
  },
  computed: {
    completedExercises() {
      return this.todayExercises.filter(ex => ex.completed).length
    },
    completionRate() {
      if (this.todayExercises.length === 0) return 0
      return Math.round((this.completedExercises / this.todayExercises.length) * 100)
    },
    filteredRecords() {
      // 简化版过滤逻辑
      return this.rehabRecords
    }
  },
  methods: {
    selectService(index) {
      const service = this.serviceCategories[index]
      uni.showToast({
        title: `选择了${service.title}`,
        icon: 'none'
      })
    },
    requestPlan() {
      uni.showToast({
        title: '康复计划申请已提交',
        icon: 'success'
      })
    },
    toggleExerciseComplete(index) {
      this.todayExercises[index].completed = !this.todayExercises[index].completed
    },
    watchVideo(exercise) {
      uni.showToast({
        title: `观看${exercise.name}视频`,
        icon: 'none'
      })
    },
    bookAppointment(therapist) {
      uni.showToast({
        title: `预约${therapist.name}`,
        icon: 'success'
      })
    },
    getStatusText(status) {
      const statusMap = {
        completed: '已完成',
        assessed: '已评估',
        pending: '待完成',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style scoped>
.rehab-center-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(33,150,243,0.08);
}

.panel-header {
  margin-bottom: 24px;
  text-align: center;
}

.panel-header h3 {
  color: #1976d2;
  margin-bottom: 8px;
}

.panel-desc {
  color: #666;
  font-size: 14px;
}

.service-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.service-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.service-card:hover {
  border-color: #2196f3;
  box-shadow: 0 4px 12px rgba(33,150,243,0.15);
}

.service-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.service-title {
  color: #1976d2;
  margin-bottom: 8px;
}

.service-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.service-stats {
  display: flex;
  justify-content: space-around;
  font-size: 12px;
  color: #888;
}

.rehab-plan-section {
  margin-bottom: 32px;
}

.current-plan {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.plan-info h5 {
  color: #1976d2;
  margin-bottom: 4px;
}

.plan-type {
  color: #666;
  font-size: 14px;
}

.progress-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: conic-gradient(#4caf50 0deg 234deg, #e0e0e0 234deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.progress-text {
  position: relative;
  z-index: 1;
  font-weight: bold;
  color: #4caf50;
  font-size: 12px;
}

.plan-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 20px;
  position: relative;
}

.timeline-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e0e0e0;
  margin-right: 16px;
  margin-top: 6px;
  flex-shrink: 0;
}

.timeline-item.completed .timeline-marker {
  background: #4caf50;
}

.timeline-item.current .timeline-marker {
  background: #2196f3;
}

.timeline-content h6 {
  color: #333;
  margin-bottom: 4px;
}

.timeline-content p {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.phase-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #888;
}

.no-plan {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.no-plan-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-plan h5 {
  color: #666;
  margin-bottom: 8px;
}

.today-training {
  margin-bottom: 32px;
}

.exercise-list {
  margin-bottom: 20px;
}

.exercise-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 12px;
}

.exercise-item.completed {
  background: #f1f8e9;
  border-color: #4caf50;
}

.exercise-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.exercise-icon {
  font-size: 24px;
  margin-right: 16px;
}

.exercise-details h6 {
  color: #333;
  margin-bottom: 4px;
}

.exercise-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.exercise-params {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #888;
}

.exercise-actions {
  display: flex;
  gap: 8px;
}

.btn-complete {
  padding: 6px 12px;
  border: 1px solid #4caf50;
  background: white;
  color: #4caf50;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.btn-complete.completed {
  background: #4caf50;
  color: white;
}

.btn-video {
  padding: 6px 12px;
  border: 1px solid #2196f3;
  background: white;
  color: #2196f3;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.training-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2196f3;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.rehab-records {
  margin-bottom: 32px;
}

.record-filters {
  margin-bottom: 16px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.record-item {
  display: flex;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 12px;
}

.record-date {
  min-width: 100px;
  margin-right: 16px;
}

.date {
  display: block;
  font-weight: bold;
  color: #333;
}

.time {
  font-size: 12px;
  color: #666;
}

.record-content {
  flex: 1;
}

.record-content h6 {
  color: #333;
  margin-bottom: 4px;
}

.record-content p {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.record-metrics {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.metric {
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  color: #1976d2;
}

.record-status {
  margin-left: 16px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.status-badge.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.assessed {
  background: #e3f2fd;
  color: #1976d2;
}

.therapist-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.therapist-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.therapist-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16px;
}

.therapist-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.therapist-info {
  flex: 1;
}

.therapist-info h6 {
  color: #333;
  margin-bottom: 4px;
}

.therapist-title {
  color: #2196f3;
  font-size: 14px;
  margin-bottom: 4px;
}

.therapist-specialty {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.therapist-rating {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #888;
}

.btn-appointment {
  padding: 8px 16px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary {
  padding: 10px 20px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
}
</style>
